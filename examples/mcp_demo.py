"""
MCP (Model Context Protocol) Integration Demo

This script demonstrates how to use MCP servers with Easy Agent Center.
"""

import asyncio
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.agents.agent_manager import Agent<PERSON><PERSON><PERSON>
from app.mcp.mcp_manager import mcp_manager
from app.mcp.examples import get_safe_example_servers, EXAMPLE_MCP_SERVERS


async def demo_mcp_integration():
    """Demonstrate MCP integration with agents."""
    print("🤖 MCP Integration Demo")
    print("=" * 50)
    
    # Create agent manager
    manager = AgentManager()
    
    print("\n📡 Setting up MCP servers...")
    
    # Get safe example servers (ones that don't require API keys)
    safe_servers = get_safe_example_servers()
    print(f"Found {len(safe_servers)} safe MCP servers to demo:")
    
    for server_config in safe_servers:
        print(f"  - {server_config.name}: {server_config.description}")
    
    # Add a simple MCP server (time server as it's most likely to work)
    time_server = EXAMPLE_MCP_SERVERS["time"]
    print(f"\n🚀 Adding MCP server: {time_server.name}")
    
    try:
        mcp_manager.add_server(time_server)
        print("✅ MCP server added successfully")
        
        # Wait a moment for the server to start
        await asyncio.sleep(2)
        
        # Check server status
        status = mcp_manager.get_server_status(time_server.server_id)
        if status:
            print(f"📊 Server Status:")
            print(f"  - Running: {status.is_running}")
            print(f"  - Connected: {status.is_connected}")
            print(f"  - Tools: {status.tool_count}")
            if status.last_error:
                print(f"  - Last Error: {status.last_error}")
        
        # Get MCP tools
        mcp_tools = mcp_manager.get_all_tools()
        print(f"\n🛠️ Available MCP tools: {len(mcp_tools)}")
        for tool in mcp_tools:
            if hasattr(tool, 'metadata') and tool.metadata:
                print(f"  - {tool.metadata.name}: {tool.metadata.description}")
        
    except Exception as e:
        print(f"⚠️ Failed to add MCP server: {e}")
        print("This might be because Node.js is not installed or the MCP server package is not available.")
        print("To install Node.js, visit: https://nodejs.org/")
        return
    
    print("\n👥 Creating MCP-enabled agent...")
    
    # Create an agent (MCP tools will be automatically included)
    try:
        agent = manager.create_agent(
            agent_id="mcp_demo_agent",
            agent_type="react",
            name="MCP Demo Agent",
            description="An agent with access to MCP tools",
            system_prompt="You are a helpful assistant with access to various tools including time utilities. Use the available tools to help users.",
            set_as_default=True
        )
        print("✅ MCP-enabled agent created successfully")
        
        # List agent tools
        agent_info = manager.list_agents()["mcp_demo_agent"]
        print(f"🔧 Agent has {len(agent_info['tools'])} tools available:")
        for tool_name in agent_info['tools']:
            print(f"  - {tool_name}")
        
    except Exception as e:
        print(f"⚠️ Failed to create agent: {e}")
        return
    
    print("\n💬 Testing MCP integration...")
    
    # Test queries that might use MCP tools
    test_queries = [
        "What time is it?",
        "What's the current date?",
        "Can you tell me about the available tools you have?"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Query: {query}")
        try:
            response = await manager.chat_with_agent(query)
            print(f"🤖 Response: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n📊 MCP Server Statistics:")
    servers = mcp_manager.list_servers()
    for server_id, status in servers.items():
        print(f"  {server_id}:")
        print(f"    - Running: {status.is_running}")
        print(f"    - Connected: {status.is_connected}")
        print(f"    - Tools: {status.tool_count}")
    
    print("\n🛑 Cleaning up...")
    await mcp_manager.shutdown_all()
    print("✅ Demo completed!")


async def demo_mcp_filesystem():
    """Demonstrate filesystem MCP server (if available)."""
    print("\n📁 Filesystem MCP Server Demo")
    print("=" * 40)
    
    # Try to add filesystem server
    fs_server = EXAMPLE_MCP_SERVERS["filesystem"]
    print(f"🚀 Adding filesystem MCP server...")
    
    try:
        mcp_manager.add_server(fs_server)
        await asyncio.sleep(2)
        
        status = mcp_manager.get_server_status(fs_server.server_id)
        if status and status.is_connected:
            print("✅ Filesystem server connected")
            
            # Create agent manager and agent
            manager = AgentManager()
            agent = manager.create_agent(
                agent_id="fs_agent",
                name="Filesystem Agent",
                description="Agent with filesystem access",
                system_prompt="You can read and write files. Be helpful with file operations."
            )
            
            # Test filesystem operations
            test_queries = [
                "List files in the /tmp directory",
                "Create a test file with some content",
                "Read the content of a file"
            ]
            
            for query in test_queries:
                print(f"\n🔍 Query: {query}")
                try:
                    response = await manager.chat_with_agent(query)
                    print(f"🤖 Response: {response}")
                except Exception as e:
                    print(f"❌ Error: {e}")
        else:
            print("❌ Filesystem server failed to connect")
            
    except Exception as e:
        print(f"⚠️ Filesystem demo failed: {e}")


def print_mcp_info():
    """Print information about available MCP servers."""
    print("📋 Available MCP Server Examples:")
    print("=" * 40)
    
    for server_id, config in EXAMPLE_MCP_SERVERS.items():
        print(f"\n🔧 {config.name} ({server_id})")
        print(f"   Description: {config.description}")
        print(f"   Command: {config.command} {' '.join(config.args)}")
        print(f"   Active: {config.is_active}")
        print(f"   Auto-start: {config.auto_start}")
        
        if config.extra_config:
            extra = config.extra_config
            if extra.get("requires_api_key"):
                print(f"   ⚠️ Requires API key: {extra.get('api_key_env', 'Unknown')}")
            if extra.get("requires_database"):
                print(f"   ⚠️ Requires database connection")
            if extra.get("platform_specific"):
                print(f"   ⚠️ Platform specific: {extra['platform_specific']}")


async def main():
    """Main demo function."""
    print("🌟 Easy Agent Center - MCP Integration Demo")
    print("=" * 60)
    
    # Print MCP server information
    print_mcp_info()
    
    # Run basic MCP demo
    await demo_mcp_integration()
    
    # Optionally run filesystem demo
    print("\n" + "=" * 60)
    choice = input("Would you like to try the filesystem MCP demo? (y/n): ").lower().strip()
    if choice == 'y':
        await demo_mcp_filesystem()
    
    print("\n🎉 All demos completed!")
    print("\nTo learn more about MCP:")
    print("- Visit: https://modelcontextprotocol.io/")
    print("- GitHub: https://github.com/modelcontextprotocol")
    print("- Install Node.js: https://nodejs.org/")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
