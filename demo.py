#!/usr/bin/env python3
"""
Easy Agent Center - Interactive Demo

This script provides an interactive demonstration of the agent system.
"""

import asyncio
import os
from agents.agent_manager import Agent<PERSON><PERSON>ger
from agents.tools import AVAILABLE_TOOLS


async def demo_basic_functionality():
    """Demonstrate basic agent functionality."""
    print("🤖 Easy Agent Center Demo")
    print("=" * 40)
    
    # Create agent manager
    manager = AgentManager()
    
    # Add tools
    print("🛠️ Adding tools to agent manager...")
    for tool in AVAILABLE_TOOLS:
        manager.add_global_tool(tool)
    print(f"✅ Added {len(AVAILABLE_TOOLS)} tools")
    
    # Create different types of agents
    print("\n👥 Creating agents...")
    
    # Create a ReAct agent (good with tools)
    react_agent = manager.create_agent(
        agent_id="react_assistant",
        agent_type="react",
        name="ReAct Assistant",
        description="A ReAct agent that can use tools effectively",
        system_prompt="You are a helpful assistant that can use various tools. Think step by step.",
        max_iterations=5,
        verbose=False
    )
    print(f"✅ Created ReAct agent: {react_agent.name}")
    
    # Create an OpenAI agent (good for general conversation)
    openai_agent = manager.create_agent(
        agent_id="openai_assistant",
        agent_type="openai",
        name="OpenAI Assistant",
        description="An OpenAI function-calling agent",
        system_prompt="You are a helpful and friendly AI assistant.",
        set_as_default=True
    )
    print(f"✅ Created OpenAI agent: {openai_agent.name}")
    
    # Show agent information
    print(f"\n📊 Agent Summary:")
    agents_info = manager.list_agents()
    for agent_id, info in agents_info.items():
        default_marker = " (DEFAULT)" if info["is_default"] else ""
        print(f"  • {agent_id}: {info['name']}{default_marker}")
        print(f"    Tools: {len(info['tools'])} available")
    
    return manager


async def demo_tool_usage(manager):
    """Demonstrate tool usage."""
    print(f"\n🛠️ Tool Usage Demo")
    print("=" * 25)
    
    # Test different tools with the ReAct agent
    test_queries = [
        ("What time is it?", "time_tool"),
        ("Calculate 15 * 23 + 100", "calculator"),
        ("What's the weather in Tokyo?", "weather_tool"),
    ]
    
    for query, tool_type in test_queries:
        print(f"\n❓ Query: {query}")
        print(f"🎯 Expected tool: {tool_type}")
        
        try:
            # Use ReAct agent for tool-heavy tasks
            response = await manager.chat_with_agent(
                message=query,
                agent_id="react_assistant"
            )
            print(f"🤖 Response: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")


async def demo_conversation(manager):
    """Demonstrate conversation capabilities."""
    print(f"\n💬 Conversation Demo")
    print("=" * 25)
    
    conversation_queries = [
        "Hello! Can you introduce yourself?",
        "What can you help me with?",
        "Tell me a short joke about robots.",
    ]
    
    for query in conversation_queries:
        print(f"\n👤 User: {query}")
        
        try:
            # Use default agent (OpenAI) for general conversation
            response = await manager.chat_with_agent(message=query)
            print(f"🤖 Assistant: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")


async def demo_agent_switching(manager):
    """Demonstrate switching between different agents."""
    print(f"\n🔄 Agent Switching Demo")
    print("=" * 30)
    
    # Same query to different agents
    query = "What's 25 multiplied by 17?"
    
    agents_to_test = ["react_assistant", "openai_assistant"]
    
    for agent_id in agents_to_test:
        agent = manager.get_agent(agent_id)
        print(f"\n🤖 Testing with {agent.name} ({agent_id}):")
        print(f"👤 User: {query}")
        
        try:
            response = await manager.chat_with_agent(
                message=query,
                agent_id=agent_id
            )
            print(f"🤖 {agent.name}: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")


def print_usage_instructions():
    """Print usage instructions."""
    print(f"\n📋 Usage Instructions")
    print("=" * 25)
    print("1. Start the FastAPI server:")
    print("   python run_server.py")
    print("   # or")
    print("   uvicorn main:app --reload")
    print()
    print("2. Access the API documentation:")
    print("   http://localhost:8000/docs")
    print()
    print("3. Test the API endpoints:")
    print("   python examples/api_examples.py")
    print()
    print("4. Run Python examples:")
    print("   python examples/basic_usage.py")
    print()
    print("5. Run tests:")
    print("   pytest tests/ -v")


async def demo_openai_compatible_services():
    """Demonstrate OpenAI-compatible services support."""
    print(f"\n🌐 OpenAI-Compatible Services Demo")
    print("=" * 45)

    manager = AgentManager()

    # Add tools
    for tool in AVAILABLE_TOOLS:
        manager.add_global_tool(tool)

    # Example configurations for different services
    service_configs = [
        {
            "id": "ollama_demo",
            "name": "Ollama Demo Agent",
            "api_base": "http://localhost:11434/v1",
            "api_key": "ollama",
            "model": "llama2",
            "description": "Demo agent using Ollama (local)"
        },
        {
            "id": "localai_demo",
            "name": "LocalAI Demo Agent",
            "api_base": "http://localhost:8080/v1",
            "api_key": "not-needed",
            "model": "gpt-3.5-turbo",
            "description": "Demo agent using LocalAI"
        },
        {
            "id": "custom_demo",
            "name": "Custom Service Demo Agent",
            "api_base": "https://api.your-service.com/v1",
            "api_key": "your-api-key",
            "model": "your-model",
            "description": "Demo agent using custom service"
        }
    ]

    print("🔧 Creating agents with different OpenAI-compatible services:")

    for config in service_configs:
        try:
            agent = manager.create_agent(
                agent_id=config["id"],
                agent_type="openai",
                name=config["name"],
                description=config["description"],
                api_base=config["api_base"],
                api_key=config["api_key"],
                model=config["model"],
                system_prompt=f"You are a helpful AI assistant powered by {config['name']}."
            )
            print(f"✅ {config['name']}")
            print(f"   API Base: {config['api_base']}")
            print(f"   Model: {config['model']}")

        except Exception as e:
            print(f"❌ Failed to create {config['name']}: {e}")

    print(f"\n📋 Configuration Examples:")
    print("• Ollama: Local LLM server (no API key needed)")
    print("• LocalAI: Self-hosted OpenAI alternative")
    print("• Custom: Any OpenAI-compatible service")
    print()
    print("💡 You can use ANY OpenAI-compatible service by setting:")
    print("  - api_base: The service's base URL")
    print("  - api_key: Your API key (or any string for local services)")
    print("  - model: The model name available in that service")


async def main():
    """Run the complete demo."""
    try:
        # Check for API key
        has_openai_key = bool(os.getenv("OPENAI_API_KEY"))

        if not has_openai_key:
            print("⚠️ Warning: OPENAI_API_KEY not set")
            print("💡 You can still use the system with OpenAI-compatible services!")
            print("   Examples: Ollama, LocalAI, vLLM, custom services")
            print("   No API key needed for local services!")
            print("\n🔄 Running demo...")

        # Run demo sections
        manager = await demo_basic_functionality()

        # Always show OpenAI-compatible services demo
        await demo_openai_compatible_services()

        if has_openai_key:
            await demo_tool_usage(manager)
            await demo_conversation(manager)
            await demo_agent_switching(manager)
        else:
            print("\n⏭️ Skipping interactive demos (no OpenAI API key)")
            print("💡 To test with local services:")
            print("   1. Install Ollama: curl -fsSL https://ollama.ai/install.sh | sh")
            print("   2. Pull a model: ollama pull llama2")
            print("   3. Start server: ollama serve")
            print("   4. Run: python examples/openai_compatible_services.py")

        print_usage_instructions()

        print(f"\n🎉 Demo completed successfully!")
        print("💡 The agents module supports ANY OpenAI-compatible service!")

    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
