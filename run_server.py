#!/usr/bin/env python3
"""
Easy Agent Center - Server Startup Script

This script starts the FastAPI server with proper configuration.
"""

import os
import sys
import uvicorn
from pathlib import Path

# Load configuration
try:
    from app.config.config import get_config
    config = get_config()
    if Path(".env").exists():
        print("✅ Loaded configuration from .env file")
except ImportError:
    # Fallback to direct environment variable loading
    try:
        from dotenv import load_dotenv

        # Look for .env file in current directory
        env_path = Path(".env")
        if env_path.exists():
            load_dotenv(env_path)
            print("✅ Loaded configuration from .env file")
    except ImportError:
        # python-dotenv not installed, skip loading .env file
        pass
    config = None

def main():
    """Start the FastAPI server."""
    print("🚀 Starting Easy Agent Center...")
    print("=" * 50)
    
    # Check if OpenAI API key is set
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  Warning: OPENAI_API_KEY environment variable not set")
        print("💡 Some agent features may not work without an API key")
        print("   Set it with: export OPENAI_API_KEY='your-key-here'")
        print()
    
    # Check if we're in the right directory
    if not Path("app/main.py").exists():
        print("❌ Error: app/main.py not found in current directory")
        print("💡 Make sure you're running this from the project root")
        sys.exit(1)
    
    print("🌐 Starting server at http://localhost:8000")
    print("📚 API documentation will be available at:")
    print("   - Swagger UI: http://localhost:8000/docs")
    print("   - ReDoc: http://localhost:8000/redoc")
    print()
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Get configuration
        if config:
            # Use configuration from config module
            server_config = config.server
            host = server_config.host
            port = server_config.port
            log_level = server_config.log_level
            reload = server_config.reload
        else:
            # Fallback to environment variables
            host = os.getenv("HOST", "0.0.0.0")
            port = int(os.getenv("PORT", "8000"))
            log_level = os.getenv("LOG_LEVEL", "info").lower()
            reload = os.getenv("ENVIRONMENT", "development") == "development"

        print(f"🌐 Starting server at http://{host}:{port}")
        if config:
            print(f"🔧 Environment: {config.environment}")
            print(f"🗄️ Database: {config.database.host}:{config.database.port}/{config.database.database}")

        # Start the server
        uvicorn.run(
            "app.main:app",
            host=host,
            port=port,
            reload=reload,
            log_level=log_level
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
