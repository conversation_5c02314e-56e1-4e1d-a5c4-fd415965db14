#!/usr/bin/env python3
"""
Debug script to diagnose the 404 error with OpenAILike LLM
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.agent_manager import Agent<PERSON>anager
from llama_index.llms.openai_like import OpenAILike

async def test_openai_like_directly():
    """Test OpenAILike directly to see if it works"""
    print("🧪 Testing OpenAILike directly...")
    
    try:
        # Test with a common local service configuration
        llm = OpenAILike(
            api_base="http://localhost:11434/v1",
            api_key="test-key",
            model="llama2",
            timeout=30.0
        )
        
        print(f"✅ OpenAILike instance created successfully")
        print(f"   - API Base: {llm.api_base}")
        print(f"   - Model: {llm.model}")
        
        # Try a simple completion
        print("\n🔄 Testing completion...")
        response = await llm.acomplete("Hello, how are you?")
        print(f"✅ Completion successful: {response}")
        
    except Exception as e:
        print(f"❌ OpenAILike test failed: {e}")
        import traceback
        traceback.print_exc()

async def test_agent_creation():
    """Test agent creation with OpenAILike"""
    print("\n🤖 Testing agent creation...")
    
    manager = AgentManager()
    
    try:
        agent = manager.create_agent(
            agent_id="test_debug_agent",
            agent_type="react",
            name="Debug Test Agent",
            api_base="http://localhost:11434/v1",
            api_key="test-key",
            model="llama2"
        )
        
        print(f"✅ Agent created successfully: {agent.name}")
        print(f"   - Type: {type(agent).__name__}")
        print(f"   - LLM: {type(agent.llm).__name__ if hasattr(agent, 'llm') else 'No LLM'}")
        
        return agent
        
    except Exception as e:
        print(f"❌ Agent creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_agent_chat(agent):
    """Test chatting with the agent"""
    if not agent:
        print("⏭️ Skipping chat test - no agent available")
        return
        
    print("\n💬 Testing agent chat...")
    
    try:
        # Test the chat functionality
        response = await agent.run(user_msg="Hello, can you introduce yourself?")
        print(f"✅ Chat successful: {response}")
        
    except Exception as e:
        print(f"❌ Chat failed: {e}")
        import traceback
        traceback.print_exc()

async def test_different_configurations():
    """Test different API configurations"""
    print("\n🔧 Testing different configurations...")
    
    configs = [
        {
            "name": "Ollama (standard)",
            "api_base": "http://localhost:11434/v1",
            "api_key": "ollama",
            "model": "llama2"
        },
        {
            "name": "Ollama (alternative)",
            "api_base": "http://127.0.0.1:11434/v1",
            "api_key": "test",
            "model": "llama2"
        },
        {
            "name": "LocalAI",
            "api_base": "http://localhost:8080/v1",
            "api_key": "not-needed",
            "model": "gpt-3.5-turbo"
        }
    ]
    
    for config in configs:
        print(f"\n📝 Testing {config['name']}...")
        try:
            llm = OpenAILike(
                api_base=config["api_base"],
                api_key=config["api_key"],
                model=config["model"],
                timeout=10.0  # Short timeout for testing
            )
            
            # Try to get model info or make a simple request
            print(f"   ✅ LLM created: {config['api_base']}")
            
            # Test a very simple completion
            try:
                response = await llm.acomplete("Hi")
                print(f"   ✅ Completion works: {str(response)[:50]}...")
            except Exception as e:
                print(f"   ❌ Completion failed: {e}")
                
        except Exception as e:
            print(f"   ❌ Configuration failed: {e}")

async def check_service_availability():
    """Check if common services are available"""
    print("\n🌐 Checking service availability...")
    
    import aiohttp
    
    services = [
        ("Ollama", "http://localhost:11434/v1/models"),
        ("LocalAI", "http://localhost:8080/v1/models"),
        ("Custom Service", "http://localhost:8000/v1/models")
    ]
    
    async with aiohttp.ClientSession() as session:
        for name, url in services:
            try:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        print(f"   ✅ {name} is available at {url}")
                    else:
                        print(f"   ⚠️ {name} responded with status {response.status}")
            except Exception as e:
                print(f"   ❌ {name} is not available: {e}")

async def main():
    """Main debug function"""
    print("🔍 Debugging OpenAILike 404 Error")
    print("=" * 50)
    
    # Check service availability first
    await check_service_availability()
    
    # Test different configurations
    await test_different_configurations()
    
    # Test OpenAILike directly
    await test_openai_like_directly()
    
    # Test agent creation
    agent = await test_agent_creation()
    
    # Test agent chat
    await test_agent_chat(agent)
    
    print("\n🎯 Debug completed!")
    print("\n💡 Troubleshooting tips:")
    print("1. Make sure your service (Ollama, LocalAI, etc.) is running")
    print("2. Check that the API endpoint URL is correct")
    print("3. Verify the model name exists in your service")
    print("4. Check firewall/network connectivity")

if __name__ == "__main__":
    asyncio.run(main())
