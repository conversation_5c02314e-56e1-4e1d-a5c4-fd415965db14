"""
Database configuration for MySQL integration.
"""

import os
from pathlib import Path
from app.config.config import DatabaseConfig

def get_database_config() -> DatabaseConfig:
    """
    Get database configuration from environment variables.

    Environment variables:
    - DB_HOST: Database host (default: localhost)
    - DB_PORT: Database port (default: 3306)
    - DB_USERNAME: Database username (default: root)
    - DB_PASSWORD: Database password (default: empty)
    - DB_DATABASE: Database name (default: easy_agent_center)
    - DB_CHARSET: Database charset (default: utf8mb4)
    - DB_POOL_SIZE: Connection pool size (default: 10)
    - DB_MAX_OVERFLOW: Max overflow connections (default: 20)
    - DB_POOL_TIMEOUT: Pool timeout in seconds (default: 30)
    - DB_POOL_RECYCLE: Pool recycle time in seconds (default: 3600)
    - DB_ECHO: Enable SQL echo for debugging (default: False)
    """
    return DatabaseConfig(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", "3306")),
        username=os.getenv("DB_USERNAME", "root"),
        password=os.getenv("DB_PASSWORD", ""),
        database=os.getenv("DB_DATABASE", "easy_agent_center"),
        charset=os.getenv("DB_CHARSET", "utf8mb4"),
        pool_size=int(os.getenv("DB_POOL_SIZE", "10")),
        max_overflow=int(os.getenv("DB_MAX_OVERFLOW", "20")),
        pool_timeout=int(os.getenv("DB_POOL_TIMEOUT", "30")),
        pool_recycle=int(os.getenv("DB_POOL_RECYCLE", "3600")),
        echo=os.getenv("DB_ECHO", "false").lower() == "true"
    )


# Default configuration instance
default_config = get_database_config()


# For backward compatibility, also import from the main config module
try:
    from app.config import get_config

    def get_database_config_from_main() -> DatabaseConfig:
        """Get database config from main config module."""
        return get_config().database

except ImportError:
    # Main config module not available, use local implementation
    pass
