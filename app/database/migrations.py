"""
Database migration utilities for Easy Agent Center.
"""

import asyncio
import os
import subprocess
import sys
from pathlib import Path
from typing import Optional

from alembic import command
from alembic.config import Config
from sqlalchemy import text

from .connection import DatabaseManager
from .config import get_database_config
from app.logger.logger import get_logger

logger = get_logger("database.migrations")


class MigrationManager:
    """Manages database migrations using Alembic."""
    
    def __init__(self):
        """Initialize migration manager."""
        self.project_root = Path(__file__).parent.parent.parent  # Go up to project root
        self.alembic_cfg_path = self.project_root / "alembic.ini"
        self.alembic_cfg = Config(str(self.alembic_cfg_path))
        
        # Set the database URL in alembic config
        db_config = get_database_config()
        self.alembic_cfg.set_main_option("sqlalchemy.url", db_config.sync_database_url)
    
    def create_migration(self, message: str, autogenerate: bool = True) -> str:
        """
        Create a new migration.
        
        Args:
            message: Migration message/description
            autogenerate: Whether to auto-generate migration from model changes
            
        Returns:
            str: The revision ID of the created migration
        """
        try:
            logger.info(f"Creating migration: {message}")
            
            if autogenerate:
                # Auto-generate migration from model changes
                revision = command.revision(
                    self.alembic_cfg,
                    message=message,
                    autogenerate=True
                )
            else:
                # Create empty migration
                revision = command.revision(
                    self.alembic_cfg,
                    message=message
                )
            
            logger.info(f"Created migration: {revision.revision}")
            return revision.revision
            
        except Exception as e:
            logger.error(f"Failed to create migration: {e}")
            raise
    
    def upgrade(self, revision: str = "head") -> None:
        """
        Upgrade database to a specific revision.
        
        Args:
            revision: Target revision (default: "head" for latest)
        """
        try:
            logger.info(f"Upgrading database to revision: {revision}")
            command.upgrade(self.alembic_cfg, revision)
            logger.info("Database upgrade completed successfully")
            
        except Exception as e:
            logger.error(f"Failed to upgrade database: {e}")
            raise
    
    def downgrade(self, revision: str) -> None:
        """
        Downgrade database to a specific revision.
        
        Args:
            revision: Target revision
        """
        try:
            logger.info(f"Downgrading database to revision: {revision}")
            command.downgrade(self.alembic_cfg, revision)
            logger.info("Database downgrade completed successfully")
            
        except Exception as e:
            logger.error(f"Failed to downgrade database: {e}")
            raise
    
    def get_current_revision(self) -> Optional[str]:
        """Get current database revision."""
        try:
            from alembic.runtime.migration import MigrationContext
            from sqlalchemy import create_engine
            
            db_config = get_database_config()
            engine = create_engine(db_config.sync_database_url)
            
            with engine.connect() as conn:
                context = MigrationContext.configure(conn)
                current_rev = context.get_current_revision()
                
            engine.dispose()
            return current_rev
            
        except Exception as e:
            logger.warning(f"Could not get current revision: {e}")
            return None
    
    def get_migration_history(self) -> list:
        """Get migration history."""
        try:
            from alembic.script import ScriptDirectory
            
            script_dir = ScriptDirectory.from_config(self.alembic_cfg)
            revisions = []
            
            for revision in script_dir.walk_revisions():
                revisions.append({
                    "revision": revision.revision,
                    "down_revision": revision.down_revision,
                    "message": revision.doc,
                    "create_date": getattr(revision, 'create_date', None)
                })
            
            return revisions
            
        except Exception as e:
            logger.error(f"Failed to get migration history: {e}")
            return []
    
    def check_migration_status(self) -> dict:
        """Check migration status."""
        try:
            current_rev = self.get_current_revision()
            history = self.get_migration_history()
            
            if not history:
                return {
                    "status": "no_migrations",
                    "current_revision": current_rev,
                    "pending_migrations": 0,
                    "message": "No migrations found"
                }
            
            latest_rev = history[0]["revision"] if history else None
            
            if current_rev is None:
                return {
                    "status": "not_initialized",
                    "current_revision": None,
                    "latest_revision": latest_rev,
                    "pending_migrations": len(history),
                    "message": "Database not initialized with migrations"
                }
            
            if current_rev == latest_rev:
                return {
                    "status": "up_to_date",
                    "current_revision": current_rev,
                    "latest_revision": latest_rev,
                    "pending_migrations": 0,
                    "message": "Database is up to date"
                }
            
            # Count pending migrations
            pending_count = 0
            found_current = False
            for rev in history:
                if rev["revision"] == current_rev:
                    found_current = True
                    break
                pending_count += 1
            
            if not found_current:
                return {
                    "status": "unknown",
                    "current_revision": current_rev,
                    "latest_revision": latest_rev,
                    "pending_migrations": "unknown",
                    "message": "Current revision not found in migration history"
                }
            
            return {
                "status": "pending_migrations",
                "current_revision": current_rev,
                "latest_revision": latest_rev,
                "pending_migrations": pending_count,
                "message": f"{pending_count} pending migration(s)"
            }
            
        except Exception as e:
            logger.error(f"Failed to check migration status: {e}")
            return {
                "status": "error",
                "message": f"Error checking migration status: {e}"
            }
    
    def auto_upgrade(self) -> bool:
        """
        Automatically upgrade database if there are pending migrations.

        Returns:
            bool: True if upgrade was performed or not needed, False if failed
        """
        try:
            status = self.check_migration_status()

            if status["status"] in ["up_to_date", "no_migrations"]:
                logger.info("Database is already up to date")
                return True

            if status["status"] == "not_initialized":
                logger.info("Initializing database with migrations")
                self.upgrade("head")
                return True

            if status["status"] == "pending_migrations":
                logger.info(f"Applying {status['pending_migrations']} pending migration(s)")
                self.upgrade("head")
                return True

            logger.warning(f"Unknown migration status: {status}")
            return False

        except Exception as e:
            logger.error(f"Auto upgrade failed: {e}")
            return False


# Global migration manager instance
_migration_manager: Optional[MigrationManager] = None


def get_migration_manager() -> MigrationManager:
    """Get global migration manager instance."""
    global _migration_manager
    if _migration_manager is None:
        _migration_manager = MigrationManager()
    return _migration_manager
