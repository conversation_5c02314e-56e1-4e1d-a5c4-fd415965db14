"""
Database models for Easy Agent Center.
"""

from typing import Dict, Any
from datetime import datetime

from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, Boolean, JSON, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class Agent(Base):
    """Agent model for storing agent configurations."""
    
    __tablename__ = "agents"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    agent_id = Column(String(255), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    agent_type = Column(String(50), nullable=False, default="react")
    system_prompt = Column(Text, nullable=True)
    model = Column(String(255), nullable=False, default="gpt-3.5-turbo")
    api_base = Column(String(500), nullable=True)
    api_key = Column(String(500), nullable=True)
    is_default = Column(Boolean, default=False, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    tools = Column(JSON, nullable=True)  # Store tools as JSON array
    config = Column(JSON, nullable=True)  # Store additional configuration
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Note: Relationships removed to avoid foreign key constraints
    # Use service layer methods to query related chat_histories manually
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert agent to dictionary."""
        return {
            "id": self.id,
            "agent_id": self.agent_id,
            "name": self.name,
            "description": self.description,
            "agent_type": self.agent_type,
            "system_prompt": self.system_prompt,
            "model": self.model,
            "api_base": self.api_base,
            "is_default": self.is_default,
            "is_active": self.is_active,
            "tools": self.tools,
            "config": self.config,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }
    
    def __repr__(self):
        return f"<Agent(agent_id='{self.agent_id}', name='{self.name}')>"


class ChatHistory(Base):
    """Chat history model for storing conversation sessions."""
    
    __tablename__ = "chat_histories"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(255), nullable=False, index=True)
    agent_id = Column(String(255), nullable=False, index=True)
    title = Column(String(500), nullable=True)  # Optional conversation title
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Note: Relationships removed to avoid foreign key constraints
    # Use service layer methods to query related agent and messages manually
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert chat history to dictionary."""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "agent_id": self.agent_id,
            "title": self.title,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None,
            "message_count": len(self.messages) if self.messages else 0
        }
    
    def __repr__(self):
        return f"<ChatHistory(session_id='{self.session_id}', agent_id='{self.agent_id}')>"


class ChatMessage(Base):
    """Chat message model for storing individual messages."""
    
    __tablename__ = "chat_messages"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    chat_history_id = Column(Integer, nullable=False, index=True)
    role = Column(String(20), nullable=False)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    message_metadata = Column(JSON, nullable=True)  # Store additional message metadata
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # Note: Relationships removed to avoid foreign key constraints
    # Use service layer methods to query related chat_history manually
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert chat message to dictionary."""
        return {
            "id": self.id,
            "chat_history_id": self.chat_history_id,
            "role": self.role,
            "content": self.content,
            "metadata": self.message_metadata,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None
        }
    
    def __repr__(self):
        return f"<ChatMessage(role='{self.role}', content='{self.content[:50]}...')>"


class LLMProvider(Base):
    """LLM Provider model for storing LLM provider configurations."""

    __tablename__ = "llm_providers"

    id = Column(Integer, primary_key=True, autoincrement=True)
    provider_id = Column(String(255), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    provider_type = Column(String(50), nullable=False)  # openai, openai_compatible, azure_openai, etc.
    api_base = Column(String(500), nullable=True)
    api_key = Column(String(500), nullable=True)
    api_version = Column(String(50), nullable=True)
    organization = Column(String(255), nullable=True)
    default_model = Column(String(255), nullable=False, default="gpt-3.5-turbo")
    context_window = Column(Integer, default=4096, nullable=False)
    max_tokens = Column(Integer, nullable=True)
    temperature = Column(Float, default=0.7, nullable=False)
    timeout = Column(Integer, default=60, nullable=False)
    max_retries = Column(Integer, default=3, nullable=False)
    is_chat_model = Column(Boolean, default=True, nullable=False)
    is_function_calling_model = Column(Boolean, default=False, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    extra_params = Column(JSON, nullable=True)  # Store additional provider-specific parameters
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    def to_dict(self) -> Dict[str, Any]:
        """Convert LLM provider to dictionary."""
        return {
            "id": self.id,
            "provider_id": self.provider_id,
            "name": self.name,
            "description": self.description,
            "provider_type": self.provider_type,
            "api_base": self.api_base,
            "api_key": self.api_key,  # Note: In production, you might want to mask this
            "api_version": self.api_version,
            "organization": self.organization,
            "default_model": self.default_model,
            "context_window": self.context_window,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "is_chat_model": self.is_chat_model,
            "is_function_calling_model": self.is_function_calling_model,
            "is_active": self.is_active,
            "is_default": self.is_default,
            "extra_params": self.extra_params,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }

    def to_dict_safe(self) -> Dict[str, Any]:
        """Convert LLM provider to dictionary without sensitive information."""
        result = self.to_dict()
        # Mask sensitive information
        if result.get("api_key"):
            result["api_key"] = "***masked***"
        return result

    def __repr__(self):
        return f"<LLMProvider(provider_id='{self.provider_id}', name='{self.name}', type='{self.provider_type}')>"


class MCPServer(Base):
    """MCP Server configuration model."""
    __tablename__ = "mcp_servers"

    id = Column(Integer, primary_key=True, autoincrement=True)
    server_id = Column(String(255), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    command = Column(String(500), nullable=False)
    args = Column(JSON, nullable=True, default=list)  # List of command arguments
    env = Column(JSON, nullable=True, default=dict)  # Environment variables
    transport = Column(String(50), nullable=False, default="stdio")  # Transport type
    sse_url = Column(String(500), nullable=True)  # SSE endpoint URL
    port = Column(Integer, nullable=True)  # Port number for server
    is_active = Column(Boolean, default=True, nullable=False)
    auto_start = Column(Boolean, default=True, nullable=False)
    timeout = Column(Integer, default=30, nullable=False)
    max_retries = Column(Integer, default=3, nullable=False)
    extra_config = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.id,
            "server_id": self.server_id,
            "name": self.name,
            "description": self.description,
            "command": self.command,
            "args": self.args or [],
            "env": self.env or {},
            "transport": self.transport,
            "sse_url": self.sse_url,
            "port": self.port,
            "is_active": self.is_active,
            "auto_start": self.auto_start,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "extra_config": self.extra_config,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }

    def to_dict_safe(self) -> Dict[str, Any]:
        """Convert to dictionary without sensitive information."""
        return {
            "id": self.id,
            "server_id": self.server_id,
            "name": self.name,
            "description": self.description,
            "command": self.command,
            "args": self.args or [],
            "transport": self.transport,
            "sse_url": self.sse_url,
            "port": self.port,
            "is_active": self.is_active,
            "auto_start": self.auto_start,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }

    def __repr__(self):
        return f"<MCPServer(server_id='{self.server_id}', name='{self.name}', command='{self.command}')>"
