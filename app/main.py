from fastapi import FastAPI
from pathlib import Path

# Load environment variables from .env file
try:
    from dotenv import load_dotenv

    # Look for .env file in current directory
    env_path = Path(".env")
    if env_path.exists():
        load_dotenv(env_path)
except ImportError:
    # python-dotenv not installed, skip loading .env file
    pass

from app.agents.agent_manager import agent_manager
from app.logger.logger import get_logger, setup_logging
from app.database import get_database_manager

from app.llm import llm_router
from app.agents import agent_router
from app.mcp.api import router as mcp_router

# Initialize with a default agent on startup
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app_instance: FastAPI):
    """Initialize the application with database and default agent."""
    # Initialize logging
    setup_logging()
    logger = get_logger("main")
    logger.info("🚀 Starting Easy Agent Center FastAPI application")

    # Initialize database
    db_manager = get_database_manager()
    try:
        await db_manager.initialize()
        logger.info("✅ Database initialized successfully")
        print("✅ Database initialized successfully")
    except Exception as e:
        logger.error("Failed to initialize database", exception=e)
        print(f"⚠️ Warning: Database initialization failed: {e}")

    try:
        # Check if default agent already exists in database
        from app.database.services import AgentService
        db_manager = get_database_manager()

        existing_agent = None
        try:
            async with db_manager.get_session() as session:
                existing_agent = await AgentService.get_agent_by_id("default", session)
        except Exception as db_e:
            logger.warning(f"Could not check database for existing agent: {db_e}")

        if existing_agent:
            logger.info("✅ Default agent already exists in database")
            print("✅ Default agent already exists in database")
        else:
            # Create a default OpenAI agent
            agent_manager.create_agent(
                agent_id="default",
                name="Default Assistant",
                description="A helpful AI assistant",
                system_prompt="You are a helpful AI assistant. Provide clear and concise responses.",
                set_as_default=True
            )
            logger.info("✅ Default agent created successfully")
            print("✅ Default agent created successfully")
    except Exception as e:
        logger.error("Could not create default agent", exception=e)
        print(f"⚠️ Warning: Could not create default agent: {e}")

    yield

    # Cleanup
    try:
        await db_manager.close()
        logger.info("✅ Database connections closed")
    except Exception as e:
        logger.error("Error closing database connections", exception=e)

    logger.info("🛑 Shutting down Easy Agent Center")
    print("👋 Easy Agent Center shutdown complete")

app = FastAPI(
    title="Easy Agent Center",
    description="A FastAPI application for managing and interacting with LlamaIndex agents",
    version="0.1.0",
    lifespan=lifespan
)

# Register LLM, Agent, and MCP routers
app.include_router(llm_router)
app.include_router(agent_router)
app.include_router(mcp_router)

@app.get("/")
async def read_root():
    return {
        "message": "Welcome to Easy Agent Center!",
        "description": "A FastAPI application for managing and interacting with LlamaIndex agents",
        "endpoints": {
            "llm_providers": "/llms",
            "agents": "/agents",
            "mcp_servers": "/mcp/servers",
            "chat": "/agents/chat",
            "stream_chat": "/agents/chat/stream",
            "chat_history": "/agents/chat/history"
        }
    }

# Initialize API logger
api_logger = get_logger("api")


