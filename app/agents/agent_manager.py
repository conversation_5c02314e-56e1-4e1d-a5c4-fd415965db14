"""
Agent Manager for managing multiple agents and their lifecycle.
"""

import asyncio
from typing import Dict, List, Optional, Any, Union
from llama_index.core.tools import BaseTool
from llama_index.core.llms import LLM
from llama_index.core.memory import BaseMemory
from llama_index.core.agent.workflow import ReActAgent
from ..logger.logger import get_logger, PerformanceLogger
from app.database.services import AgentService, ChatService
from ..llm import get_llm_manager
from llama_index.core.workflow import Context
from llama_index.core.agent.workflow import AgentStream, ToolCallResult
from ..mcp.mcp_manager import mcp_manager

class AgentManager:
    """
    Manager class for handling multiple agents.

    This class provides functionality to create, configure, and manage
    multiple agents of different types, allowing for easy switching
    between agents and coordinated multi-agent workflows.
    """

    # Registry of available agent types
    AGENT_TYPES: Dict[str, str] = {
        "react": "react"
    }

    def __init__(self):
        """Initialize the agent manager."""
        self._agents: Dict[str, ReActAgent] = {}
        self._default_agent_id: Optional[str] = None
        self._global_tools: List[BaseTool] = []
        self._global_llm: Optional[LLM] = None
        self._global_memory: Optional[BaseMemory] = None

        # Initialize LLM manager
        self._llm_manager = get_llm_manager()

        # Initialize logger
        self.logger = get_logger("agents.AgentManager")
        self.logger.info("🚀 AgentManager initialized")

        # Initialize MCP manager reference
        self._mcp_manager = mcp_manager

    def set_global_llm(self, llm: LLM) -> None:
        """
        Set a global LLM to be used by all agents by default.
        
        Args:
            llm: The language model to use globally
        """
        self._global_llm = llm

    def set_global_memory(self, memory: BaseMemory) -> None:
        """
        Set a global memory system to be used by all agents by default.

        Args:
            memory: The memory system to use globally
        """
        self._global_memory = memory

    def get_llm_manager(self):
        """Get the LLM manager instance."""
        return self._llm_manager

    async def list_llm_providers(self) -> List[str]:
        """List all available LLM providers."""
        return await self._llm_manager.list_providers()

    async def get_llm_provider_info(self, provider_id: str) -> Optional[LLM]:
        """Get information about a specific LLM provider."""
        return await self._llm_manager.get_provider_info(provider_id)

    def validate_llm_configuration(self) -> List[str]:
        """Validate LLM configuration and return any issues."""
        return self._llm_manager.validate_configuration()

    def clear_llm_cache(self) -> None:
        """Clear the LLM instance cache."""
        self._llm_manager.clear_cache()

    def get_llm_cache_stats(self) -> Dict[str, Any]:
        """Get LLM cache statistics."""
        return self._llm_manager.get_cache_stats()

    def add_global_tool(self, tool: BaseTool) -> None:
        """
        Add a tool that will be available to all agents.

        Note: LlamaIndex agents don't support dynamic tool addition.
        Tools must be provided at agent creation time.

        Args:
            tool: The tool to add globally
        """
        try:
            tool_name = getattr(tool.metadata, 'name', 'unknown') if hasattr(tool, 'metadata') else 'unknown'
            self._global_tools.append(tool)

            self.logger.log_tool_operation("added globally", tool_name)
            self.logger.warning("Note: Existing agents won't have this tool. Recreate agents to include new tools.")

        except Exception as e:
            self.logger.error("Failed to add global tool", exception=e)
            raise

    def remove_global_tool(self, tool_name: str) -> bool:
        """
        Remove a global tool by name.

        Note: LlamaIndex agents don't support dynamic tool removal.
        This only removes from the global tools list for future agents.

        Args:
            tool_name: Name of the tool to remove

        Returns:
            bool: True if tool was removed, False if not found
        """
        try:
            for i, tool in enumerate(self._global_tools):
                if tool.metadata.name == tool_name:
                    self._global_tools.pop(i)

                    self.logger.log_tool_operation("removed globally", tool_name)
                    self.logger.warning("Note: Existing agents still have this tool. Recreate agents to remove tools.")
                    return True

            self.logger.warning(f"Global tool '{tool_name}' not found for removal")
            return False

        except Exception as e:
            self.logger.error(f"Failed to remove global tool '{tool_name}'", exception=e)
            raise

    def create_agent(
        self,
        agent_id: str,
        agent_type: str = "react",
        name: Optional[str] = None,
        description: Optional[str] = None,
        tools: Optional[List[BaseTool]] = None,
        memory: Optional[BaseMemory] = None,
        system_prompt: Optional[str] = None,
        set_as_default: bool = False,
        llm_id: Optional[str] = None,
        api_base: Optional[str] = None,
        api_key: Optional[str] = None,
        model: str = "gpt-3.5-turbo",
        **kwargs
    ) -> ReActAgent:
        """
        Create a new agent.

        Args:
            agent_id: Unique identifier for the agent
            agent_type: Type of agent to create ("react" or "openai")
            name: Agent name (defaults to agent_id)
            description: Agent description
            tools: List of tools (global tools are always included)
            memory: Memory system (uses global memory if not provided)
            system_prompt: System prompt for the agent
            set_as_default: Whether to set this as the default agent
            llm_id: LLM identifier (if not provided, will create from api_base/api_key/model or use global LLM)
            api_base: Custom API base URL for OpenAI-compatible services
            api_key: API key for the service
            model: Model name to use
            **kwargs: Additional configuration parameters

        Returns:
            ReActAgent: The created agent

        Raises:
            ValueError: If agent_id already exists or agent_type is invalid
        """
        try:
            self.logger.info(f"🔄 Creating agent '{agent_id}' of type '{agent_type}'")

            if agent_id in self._agents:
                error_msg = f"Agent with ID '{agent_id}' already exists"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            if agent_type not in self.AGENT_TYPES:
                error_msg = f"Unknown agent type '{agent_type}'. Available types: {list(self.AGENT_TYPES.keys())}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # Use defaults if not provided
            if name is None:
                name = agent_id
            if description is None:
                description = f"Agent {agent_id}"
                
            # Create LLM instance using LLMManager
            if llm_id is not None:
                # Use specific provider by name
                llm = self._llm_manager.create_llm(provider_name=llm_id, model=model)
            elif api_base or api_key:
                # Create LLM from direct parameters
                llm = self._llm_manager.create_llm_from_params(
                    api_base=api_base,
                    api_key=api_key,
                    model=model
                )
            else:
                # Use default provider or global LLM
                if self._global_llm is None:
                    # Create default LLM using LLMManager
                    try:
                        llm = self._llm_manager.create_llm(model=model)
                        self.set_global_llm(llm)
                    except Exception as e:
                        self.logger.warning(f"Failed to create LLM from LLMManager: {e}")
                        # Fallback to hardcoded default if LLMManager fails
                        llm = self._llm_manager.create_llm_from_params(
                            api_base="https://ark.cn-beijing.volces.com/api/v3/",
                            api_key="af78ce47-20be-4fa6-a47a-d4e64323b9f0",
                            model="deepseek-v3-250324",
                            context_window=128000,
                            is_chat_model=True,
                            is_function_calling_model=True,
                        )
                        self.set_global_llm(llm)
                else:
                    llm = self._global_llm

            if memory is None:
                memory = self._global_memory

            # Combine global tools with agent-specific tools and MCP tools
            all_tools = self._global_tools.copy()
            if tools:
                all_tools.extend(tools)

            # Add MCP tools
            mcp_tools = self._mcp_manager.get_all_tools()
            all_tools.extend(mcp_tools)

            self.logger.debug(f"Agent '{agent_id}' will have {len(all_tools)} tools")

            # Create the agent
            with PerformanceLogger(f"create_agent_{agent_id}", self.logger):
                if agent_type == "react":
                    agent = ReActAgent(
                        name=name,
                        description=description,
                        llm=llm,
                        tools=all_tools,
                        system_prompt=system_prompt,
                        **kwargs
                    )
                else:
                    raise ValueError(f"Unknown agent type: {agent_type}")

            # Store the agent
            self._agents[agent_id] = agent

            # Set as default if requested or if it's the first agent
            if set_as_default or self._default_agent_id is None:
                self._default_agent_id = agent_id
                self.logger.info(f"🎯 Agent '{agent_id}' set as default")

            # Save to database 
            try:
                 # Run database operation in async context
                import asyncio
                loop = None
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # Save agent to database
                loop.run_until_complete(
                    AgentService.create_agent(
                        agent_id=agent_id,
                        name=name,
                        agent_type=agent_type,
                        description=description,
                        system_prompt=system_prompt,
                        model=model,
                        api_base=api_base,
                        api_key=api_key,
                        is_default=set_as_default or self._default_agent_id == agent_id,
                        tools=[tool.metadata.name for tool in all_tools if hasattr(tool, 'metadata') and tool.metadata.name],
                        config=kwargs
                    )
                )
                self.logger.info(f"💾 Agent '{agent_id}' saved to database")
            except Exception as e:
                self.logger.warning(f"Failed to save agent to database: {e}")

            self.logger.log_agent_operation("created", agent_id, f"Type: {agent_type}, Tools: {len(all_tools)}")
            return agent

        except Exception as e:
            self.logger.error(f"Failed to create agent '{agent_id}'", exception=e)
            raise

    async def get_agent(self, agent_id: str) -> Optional[ReActAgent]:
        """
        Get an agent by ID.

        Args:
            agent_id: The agent ID

        Returns:
            Union[ReActAgent, FunctionAgent]: The agent, or None if not found
        """
        # First check if agent exists in memory
        if agent_id in self._agents:
            return self._agents[agent_id]

        # If not in memory, try to load from database and recreate
        db_agent = await AgentService.get_agent_by_id(agent_id)
        if not db_agent:
            return None

        # Recreate the agent from database configuration
        try:
            # Get LLM configuration, TODO
            llm = await self._llm_manager.get_provider_info('huoshan')

            if not llm:
                self.logger.error(f"No LLM available for agent '{agent_id}'")
                return None

            # Recreate the agent
            config = db_agent.config if db_agent.config is not None else {}
            if not isinstance(config, dict):
                config = {}

            agent = ReActAgent(
                name=str(db_agent.name),
                description=str(db_agent.description) if db_agent.description is not None else '',
                llm=llm,
                tools=self._global_tools.copy(),  # Use global tools for now
                system_prompt=str(db_agent.system_prompt) if db_agent.system_prompt is not None else None,
                **config
            )

            # Store in memory for future use
            self._agents[agent_id] = agent

            return agent

        except Exception as e:
            self.logger.error(f"Failed to recreate agent '{agent_id}' from database", exception=e)
            return None

    def get_default_agent(self) -> Optional[ReActAgent]:
        """
        Get the default agent.

        Returns:
            Union[ReActAgent, FunctionAgent]: The default agent, or None if no default is set
        """
        if self._default_agent_id:
            return self._agents.get(self._default_agent_id)
        return None

    def set_default_agent(self, agent_id: str) -> bool:
        """
        Set the default agent.
        
        Args:
            agent_id: The agent ID to set as default
            
        Returns:
            bool: True if successful, False if agent not found
        """
        if agent_id in self._agents:
            self._default_agent_id = agent_id
            return True
        return False

    def list_agents(self) -> Dict[str, Dict[str, Any]]:
        """
        List all agents and their information.

        Returns:
            Dict[str, Dict[str, Any]]: Dictionary of agent information
        """
        result = {}
        for agent_id, agent in self._agents.items():
            # Create agent info dict manually since workflow agents don't have to_dict()
            agent_info = {
                "name": agent.name,
                "description": agent.description,
                "tools": [tool.metadata.name for tool in (agent.tools or []) if hasattr(tool, 'metadata')],
                "system_prompt": getattr(agent, 'system_prompt', None),
                "is_default": agent_id == self._default_agent_id,
                "type": agent.__class__.__name__
            }
            result[agent_id] = agent_info
        return result

    def remove_agent(self, agent_id: str) -> bool:
        """
        Remove an agent.
        
        Args:
            agent_id: The agent ID to remove
            
        Returns:
            bool: True if agent was removed, False if not found
        """
        if agent_id in self._agents:
            del self._agents[agent_id]
            # Clear default if it was the removed agent
            if self._default_agent_id == agent_id:
                self._default_agent_id = None
                # Set a new default if other agents exist
                if self._agents:
                    self._default_agent_id = next(iter(self._agents.keys()))
            return True
        return False

    async def chat_with_agent(
        self,
        message: str,
        agent_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> str:
        """
        Send a message to an agent and get a response.

        Args:
            message: The message to send
            agent_id: The agent ID (uses default if not provided)
            session_id: Session ID for chat history (optional)

        Returns:
            str: The agent's response

        Raises:
            ValueError: If no agent is found
        """
        try:
            agent = await self.get_agent(agent_id) if agent_id else self.get_default_agent()
            if not agent:
                error_msg = f"No agent found with ID '{agent_id}'" if agent_id else "No default agent set"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # Get actual agent_id for database operations
            actual_agent_id = agent_id or self._default_agent_id or "default"

            # Save user message to database if session_id is provided and database is available
            if session_id:
                try:
                    # Create or get chat history
                    chat_history = await ChatService.get_chat_history(session_id)
                    if not chat_history:
                        await ChatService.create_chat_history(session_id, actual_agent_id)

                    # Save user message
                    await ChatService.add_message(session_id, "user", message)
                except Exception as e:
                    self.logger.warning(f"Failed to save user message to database: {e}")

            self.logger.debug(f"💬 Routing chat to agent '{agent.name}' (ID: {actual_agent_id})")
            result = await agent.run(user_msg=message)
            response = str(result)

            # Save assistant response to database if session_id is provided and database is available
            if session_id:
                try:
                    await ChatService.add_message(session_id, "assistant", response)
                except Exception as e:
                    self.logger.warning(f"Failed to save assistant response to database: {e}")

            return response

        except Exception as e:
            self.logger.error(f"Failed to chat with agent {agent_id or 'default'}", exception=e)
            raise

    async def stream_chat_with_agent(
        self,
        message: str,
        agent_id: Optional[str] = None,
        session_id: Optional[str] = None
    ):
        """
        Send a message to an agent and get a streaming response.

        Note: Workflow agents don't support native streaming.
        This method simulates streaming by yielding the full response.

        Args:
            message: The message to send
            agent_id: The agent ID (uses default if not provided)

        Yields:
            str: Chunks of the agent's response

        Raises:
            ValueError: If no agent is found
        """
        try:
            agent = await self.get_agent(agent_id) if agent_id else self.get_default_agent()
            if not agent:
                error_msg = f"No agent found with ID '{agent_id}'" if agent_id else "No default agent set"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # Get actual agent_id for database operations
            actual_agent_id = agent_id or self._default_agent_id or "default"

            # Save user message to database if session_id is provided and database is available
            if session_id:
                try:
                    # Create or get chat history
                    chat_history = await ChatService.get_chat_history(session_id)
                    if not chat_history:
                        await ChatService.create_chat_history(session_id, actual_agent_id)

                    # Save user message
                    await ChatService.add_message(session_id, "user", message)
                except Exception as e:
                    self.logger.warning(f"Failed to save user message to database: {e}")

            self.logger.debug(f"💬 Routing chat to agent '{agent.name}' (ID: {actual_agent_id})")
            ctx = Context(agent)
            handler = agent.run(message, ctx=ctx)  # 移除 await
            async for ev in handler.stream_events():
                if isinstance(ev, ToolCallResult):
                    yield f"\nCall {ev.tool_name} with {ev.tool_kwargs}\nReturned: {ev.tool_output}"
                if isinstance(ev, AgentStream):
                    yield f"{ev.delta}"

            response = await handler
            # Save assistant response to database if session_id is provided and database is available
            if session_id:
                try:
                    await ChatService.add_message(session_id, "assistant", response)
                except Exception as e:
                    self.logger.warning(f"Failed to save assistant response to database: {e}")

        except Exception as e:
            self.logger.error(f"Failed to chat with agent {agent_id or 'default'}", exception=e)
            raise

    def reset_agent(self, agent_id: Optional[str] = None) -> bool:
        """
        Reset an agent's conversation history.

        Note: Workflow agents don't maintain persistent state between runs.
        This method is provided for API compatibility but doesn't perform any action.

        Args:
            agent_id: The agent ID (uses default if not provided)

        Returns:
            bool: True if agent exists, False if agent not found
        """
        agent = self.get_agent(agent_id) if agent_id else self.get_default_agent()
        if agent:
            self.logger.info(f"🔄 Reset requested for agent '{agent_id or 'default'}' (workflow agents are stateless)")
            return True
        return False

    def reset_all_agents(self) -> None:
        """
        Reset all agents' conversation histories.

        Note: Workflow agents don't maintain persistent state between runs.
        This method is provided for API compatibility but doesn't perform any action.
        """
        self.logger.info(f"🔄 Reset requested for all agents (workflow agents are stateless)")
        for agent_id in self._agents.keys():
            self.logger.debug(f"  - Agent '{agent_id}' (no action needed)")

    def get_mcp_manager(self):
        """Get the MCP manager instance."""
        return self._mcp_manager

    def get_mcp_tools(self, server_id: Optional[str] = None) -> List[BaseTool]:
        """
        Get MCP tools from a specific server or all servers.

        Args:
            server_id: Optional server ID to get tools from specific server

        Returns:
            List[BaseTool]: List of MCP tools
        """
        if server_id:
            return self._mcp_manager.get_server_tools(server_id)
        else:
            return self._mcp_manager.get_all_tools()

    def list_mcp_servers(self) -> Dict[str, Any]:
        """
        List all MCP servers and their status.

        Returns:
            Dict[str, Any]: Dictionary of MCP server statuses
        """
        return self._mcp_manager.list_servers()

    async def refresh_agent_tools(self, agent_id: Optional[str] = None) -> bool:
        """
        Refresh tools for an agent (recreate agent with updated tools).

        Note: This is needed because LlamaIndex agents don't support dynamic tool updates.

        Args:
            agent_id: Agent ID to refresh, or None to refresh all agents

        Returns:
            bool: True if successful
        """
        try:
            if agent_id:
                # Refresh specific agent
                if agent_id not in self._agents:
                    self.logger.warning(f"Agent '{agent_id}' not found for tool refresh")
                    return False

                # Get agent from database and recreate
                db_agent = await AgentService.get_agent_by_id(agent_id)
                if not db_agent:
                    self.logger.error(f"Agent '{agent_id}' not found in database")
                    return False

                # Remove old agent
                del self._agents[agent_id]

                # Recreate agent (this will include updated MCP tools)
                self.create_agent(
                    agent_id=db_agent.agent_id,
                    agent_type=db_agent.agent_type,
                    name=db_agent.name,
                    description=db_agent.description,
                    system_prompt=db_agent.system_prompt,
                    model=db_agent.model,
                    api_base=db_agent.api_base,
                    api_key=db_agent.api_key,
                    set_as_default=(db_agent.agent_id == self._default_agent_id),
                    **(db_agent.config if db_agent.config else {})
                )

                self.logger.info(f"🔄 Refreshed tools for agent '{agent_id}'")
                return True
            else:
                # Refresh all agents
                agent_ids = list(self._agents.keys())
                success_count = 0

                for aid in agent_ids:
                    if await self.refresh_agent_tools(aid):
                        success_count += 1

                self.logger.info(f"🔄 Refreshed tools for {success_count}/{len(agent_ids)} agents")
                return success_count == len(agent_ids)

        except Exception as e:
            self.logger.error(f"Failed to refresh agent tools for '{agent_id or 'all'}'", exception=e)
            return False


# Global agent manager instance
agent_manager = AgentManager()
