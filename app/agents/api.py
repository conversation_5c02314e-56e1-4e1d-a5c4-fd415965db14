"""
FastAPI routes for Agent management.

This module contains all the API endpoints for managing agents,
including CRUD operations, chat functionality, and agent configuration.
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from typing import List, Optional, Dict, Any
import json

from app.logger.logger import get_logger, PerformanceLogger
from app.database.services import AgentService, ChatService
from app.agents.agent_manager import agent_manager
from ..llm.models import (
    CreateAgentRequest,
    UpdateAgentRequest,
    AgentSafeResponse,
    AgentDeleteResponse,
    AgentSetDefaultResponse,
    ChatRequest,
    ChatResponse,
    ChatHistoryResponse,
    ChatMessageResponse
)

# Initialize router and logger
router = APIRouter(prefix="/agents", tags=["Agents"])
api_logger = get_logger("llm.agent_api")


# Agent management endpoints
@router.get("", response_model=List[Dict[str, Any]])
async def list_agents():
    """List all available agents."""
    try:
        agents = await AgentService.list_agents()
        return [agent.to_dict() for agent in agents]
    except Exception as e:
        api_logger.error("Failed to list agents from database", exception=e)
        raise HTTPException(status_code=500, detail=f"Error listing agents from database: {str(e)}")


@router.post("", response_model=Dict[str, Any])
async def create_agent(request: CreateAgentRequest):
    """Create a new agent."""
    try:
        with PerformanceLogger(f"api_create_agent_{request.agent_id}", api_logger):
            # Transform llm_id to api_base/api_key if needed
            api_base = request.api_base
            api_key = request.api_key
            model = request.model

            if request.llm_id and not api_base:
                # Get LLM provider configuration
                from app.database.services import LLMService
                llm_provider = await LLMService.get_llm_provider_by_id(request.llm_id)
                if llm_provider:
                    provider_dict = llm_provider.to_dict()
                    api_base = provider_dict.get("api_base")
                    api_key = provider_dict.get("api_key")
                    if not request.model or request.model == "gpt-3.5-turbo":
                        model = provider_dict.get("default_model", "gpt-3.5-turbo")

            agent = agent_manager.create_agent(
                agent_id=request.agent_id,
                agent_type=request.agent_type,
                name=request.name,
                description=request.description,
                system_prompt=request.system_prompt,
                set_as_default=request.set_as_default,
                api_base=api_base,
                api_key=api_key,
                model=model
            )

            api_logger.info(f"✅ Agent created successfully: {request.agent_id}")

            # Create agent info manually since workflow agents don't have to_dict()
            agent_info = {
                "name": agent.name,
                "description": agent.description,
                "tools": [tool.metadata.name for tool in (agent.tools or []) if hasattr(tool, 'metadata')],
                "system_prompt": getattr(agent, 'system_prompt', None),
                "type": agent.__class__.__name__
            }

            return {
                "message": f"Agent '{request.agent_id}' created successfully",
                "agent": agent_info
            }
    except ValueError as e:
        api_logger.warning(f"Agent creation failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        api_logger.error(f"Agent creation failed for {request.agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error creating agent: {str(e)}")


@router.get("/{agent_id}", response_model=Dict[str, Any])
async def get_agent(agent_id: str):
    """Get information about a specific agent."""
    try:
        agent = await agent_manager.get_agent(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_id}' not found")

        # Create agent info manually since workflow agents don't have to_dict()
        agent_info = {
            "name": agent.name,
            "description": agent.description,
            "tools": [tool.metadata.name for tool in (agent.tools or []) if hasattr(tool, 'metadata')],
            "system_prompt": getattr(agent, 'system_prompt', None),
            "type": agent.__class__.__name__
        }
        return agent_info
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get agent: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error getting agent: {str(e)}")


@router.put("/{agent_id}", response_model=Dict[str, Any])
async def update_agent(agent_id: str, request: UpdateAgentRequest):
    """Update an agent."""
    try:
        with PerformanceLogger(f"api_update_agent_{agent_id}", api_logger):
            # Check if agent exists
            agent = agent_manager.get_agent(agent_id)
            if not agent:
                raise HTTPException(status_code=404, detail=f"Agent '{agent_id}' not found")

            # For now, we'll recreate the agent with updated parameters
            # This is a simplified approach - in a full implementation, 
            # you might want to add update methods to the agent manager
            
            # Get current agent info (for future use)
            # current_info = agent_manager.list_agents().get(agent_id, {})
            
            # Update fields
            updates = {k: v for k, v in request.model_dump().items() if v is not None}
            
            if updates:
                api_logger.info(f"Updated agent: {agent_id}")
                return {"message": f"Agent '{agent_id}' updated successfully (update functionality to be implemented)"}
            else:
                raise HTTPException(status_code=400, detail="No valid fields to update")
                
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to update agent: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error updating agent: {str(e)}")


@router.delete("/{agent_id}", response_model=AgentDeleteResponse)
async def delete_agent(agent_id: str):
    """Delete an agent."""
    try:
        if agent_manager.remove_agent(agent_id):
            api_logger.info(f"Deleted agent: {agent_id}")
            return AgentDeleteResponse(message=f"Agent '{agent_id}' deleted successfully")
        else:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_id}' not found")
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to delete agent: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error deleting agent: {str(e)}")


@router.post("/{agent_id}/reset", response_model=Dict[str, str])
async def reset_agent(agent_id: str):
    """Reset an agent's conversation history."""
    try:
        if agent_manager.reset_agent(agent_id):
            api_logger.info(f"Reset agent: {agent_id}")
            return {"message": f"Agent '{agent_id}' reset successfully"}
        else:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_id}' not found")
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to reset agent: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error resetting agent: {str(e)}")


@router.post("/{agent_id}/set-default", response_model=AgentSetDefaultResponse)
async def set_default_agent(agent_id: str):
    """Set an agent as the default."""
    try:
        if agent_manager.set_default_agent(agent_id):
            api_logger.info(f"Set default agent: {agent_id}")
            return AgentSetDefaultResponse(message=f"Agent '{agent_id}' set as default successfully")
        else:
            raise HTTPException(status_code=404, detail=f"Agent '{agent_id}' not found")
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to set default agent: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error setting default agent: {str(e)}")

@router.get("/db/{agent_id}", response_model=Dict[str, Any])
async def get_agent_from_db(agent_id: str):
    """Get agent from database."""
    try:
        agent = await AgentService.get_agent_by_id(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail=f"Agent not found in database: {agent_id}")
        return agent.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get agent from database: {agent_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error getting agent from database: {str(e)}")


# Chat endpoints
@router.post("/chat", response_model=ChatResponse)
async def chat_with_agent(request: ChatRequest):
    """Send a message to an agent and get a response."""
    try:
        with PerformanceLogger("api_chat", api_logger):
            api_logger.info(f"📨 Chat request: agent_id={request.agent_id}, message_length={len(request.message)}")

            response = await agent_manager.chat_with_agent(
                message=request.message,
                agent_id=request.agent_id,
                session_id=request.session_id
            )

            # Get agent info for response
            agent = await agent_manager.get_agent(request.agent_id) if request.agent_id else agent_manager.get_default_agent()
            if not agent:
                api_logger.error(f"Agent not found: {request.agent_id}")
                raise HTTPException(status_code=404, detail="Agent not found")

            api_logger.info(f"✅ Chat response sent: agent={agent.name}, response_length={len(response)}")

            return ChatResponse(
                response=response,
                agent_id=request.agent_id or "default",
                agent_name=agent.name
            )
    except ValueError as e:
        api_logger.warning(f"Chat request failed: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        api_logger.error("Chat request failed", exception=e)
        raise HTTPException(status_code=500, detail=f"Error processing chat: {str(e)}")

@router.post("/chat/stream")
async def stream_chat_with_agent(request: ChatRequest):
    """Send a message to an agent and get a streaming response."""
    try:
        async def generate_response():
            async for chunk in agent_manager.stream_chat_with_agent(
                message=request.message,
                agent_id=request.agent_id,
                session_id=request.session_id
            ):
                yield f"data: {json.dumps({'chunk': chunk})}\n\n"

        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing stream chat: {str(e)}")


@router.get("/chat/history", response_model=List[ChatHistoryResponse])
async def list_chat_histories(agent_id: Optional[str] = None, limit: int = 50):
    """List chat histories."""
    try:
        histories = await ChatService.list_chat_histories(agent_id=agent_id, limit=limit)
        return [ChatHistoryResponse(**history.to_dict()) for history in histories]
    except Exception as e:
        api_logger.error("Failed to list chat histories", exception=e)
        raise HTTPException(status_code=500, detail=f"Error listing chat histories: {str(e)}")


@router.get("/chat/history/{session_id}", response_model=ChatHistoryResponse)
async def get_chat_history(session_id: str):
    """Get a specific chat history."""
    try:
        history = await ChatService.get_chat_history(session_id)
        if not history:
            raise HTTPException(status_code=404, detail=f"Chat history not found: {session_id}")
        return ChatHistoryResponse(**history.to_dict())
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get chat history {session_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error getting chat history: {str(e)}")


@router.get("/chat/history/{session_id}/messages", response_model=List[ChatMessageResponse])
async def get_chat_messages(session_id: str, limit: int = 100):
    """Get messages for a chat session."""
    try:
        messages = await ChatService.get_messages(session_id, limit=limit)
        return [ChatMessageResponse(**message.to_dict()) for message in messages]
    except Exception as e:
        api_logger.error(f"Failed to get messages for session {session_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error getting messages: {str(e)}")
