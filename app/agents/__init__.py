"""
Easy Agent Center - LlamaIndex Agents Module

This module provides a comprehensive agent system built on top of LlamaIndex,
offering various types of agents for different use cases.
"""

from .agent_manager import AgentManager
from ..logger.logger import get_logger, setup_logging, PerformanceLogger, log_exception
from .api import router as agent_router

# Import LlamaIndex agents directly
from llama_index.core.agent.workflow import ReActAgent, FunctionAgent

__all__ = [
    "AgentManager",
    "ReActAgent",
    "FunctionAgent",
    "get_logger",
    "setup_logging",
    "PerformanceLogger",
    "log_exception",
     "agent_router"
]

__version__ = "0.1.0"
