"""
LLM Manager for centralized LLM instance management.

This module provides the main LLMManager class that handles LLM instance
creation, caching, and lifecycle management.
"""

import time
import logging
from typing import Dict, Optional, Any, List, Tuple
from threading import Lock
from llama_index.core.llms import LLM

from .llm_config import LLMConfig, LLMProviderConfig, LLMProviderType
from .providers import create_provider, LLMProvider
from app.database.services import LLMService

logger = logging.getLogger("llm.manager")


class LLMCache:
    """Simple LLM instance cache with TTL support."""
    
    def __init__(self, ttl: int = 3600):
        """Initialize cache with TTL in seconds."""
        self.ttl = ttl
        self._cache: Dict[str, Tuple[LLM, float]] = {}
        self._lock = Lock()
    
    def get(self, key: str) -> Optional[LLM]:
        """Get LLM instance from cache if not expired."""
        with self._lock:
            if key in self._cache:
                llm, timestamp = self._cache[key]
                if time.time() - timestamp < self.ttl:
                    return llm
                else:
                    # Remove expired entry
                    del self._cache[key]
        return None
    
    def put(self, key: str, llm: LLM) -> None:
        """Put LLM instance in cache."""
        with self._lock:
            self._cache[key] = (llm, time.time())
    
    def remove(self, key: str) -> bool:
        """Remove LLM instance from cache."""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
        return False
    
    def clear(self) -> None:
        """Clear all cached instances."""
        with self._lock:
            self._cache.clear()
    
    def size(self) -> int:
        """Get cache size."""
        with self._lock:
            return len(self._cache)
    
    def cleanup_expired(self) -> int:
        """Remove expired entries and return count of removed items."""
        current_time = time.time()
        expired_keys = []
        
        with self._lock:
            for key, (_, timestamp) in self._cache.items():
                if current_time - timestamp >= self.ttl:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
        
        return len(expired_keys)


class LLMManager:
    """Main LLM manager class for centralized LLM management."""
    
    def __init__(self, config: Optional[LLMConfig] = None):
        """Initialize LLM manager."""
        self.config = config or LLMConfig.from_env()
        self.cache = LLMCache(ttl=self.config.cache_ttl) if self.config.cache_enabled else None
        self._providers: Dict[str, LLMProvider] = {}
        self._lock = Lock()
        
        self.logger = logging.getLogger("llm.LLMManager")
        self.logger.info("🚀 LLMManager initialized")
        
        # Initialize providers
        self._initialize_providers()
    
    def _initialize_providers(self) -> None:
        """Initialize provider instances from configuration."""
        for name, provider_config in self.config.providers.items():
            try:
                provider = create_provider(provider_config)
                self._providers[name] = provider
                self.logger.info(f"Initialized provider: {name} ({provider_config.provider_type.value})")
            except Exception:
                self.logger.error(f"Failed to initialize provider {name}", exc_info=True)
    
    def _get_cache_key(self, provider_name: str, model: str, **kwargs) -> str:
        """Generate cache key for LLM instance."""
        # Create a deterministic key from parameters
        key_parts = [provider_name, model]
        
        # Add relevant kwargs to key
        for key in sorted(kwargs.keys()):
            if key in ["temperature", "max_tokens", "context_window"]:
                key_parts.append(f"{key}={kwargs[key]}")
        
        return "|".join(key_parts)
    
    def create_llm(
        self,
        provider_name: Optional[str] = None,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> LLM:
        """
        Create or retrieve LLM instance.
        
        Args:
            provider_name: Name of the provider to use (uses default if None)
            model: Model name to use (uses provider default if None)
            use_cache: Whether to use cached instance if available
            **kwargs: Additional parameters for LLM creation
        
        Returns:
            LLM: The LLM instance
        
        Raises:
            ValueError: If provider not found or configuration invalid
        """
        # Use default provider if not specified
        if provider_name is None:
            provider_name = self.config.default_provider
        
        if not provider_name:
            raise ValueError("No provider specified and no default provider configured")
        
        # Get provider
        provider = self._providers.get(provider_name)
        if not provider:
            raise ValueError(f"Provider '{provider_name}' not found")
        
        # Use provider's default model if not specified
        if model is None:
            model = provider.config.default_model
        
        # Check cache first
        cache_key = self._get_cache_key(provider_name, model, **kwargs)
        if use_cache and self.cache:
            cached_llm = self.cache.get(cache_key)
            if cached_llm:
                self.logger.debug(f"Retrieved LLM from cache: {cache_key}")
                return cached_llm
        
        # Create new LLM instance
        try:
            llm = provider.create_llm(model=model, **kwargs)
            
            # Cache the instance
            if use_cache and self.cache:
                self.cache.put(cache_key, llm)
                self.logger.debug(f"Cached LLM instance: {cache_key}")
            
            self.logger.info(f"Created LLM: provider={provider_name}, model={model}")
            return llm
            
        except Exception:
            self.logger.error(f"Failed to create LLM: provider={provider_name}, model={model}", exc_info=True)
            raise
    
    def create_llm_from_params(
        self,
        api_base: Optional[str] = None,
        api_key: Optional[str] = None,
        model: str = "gpt-3.5-turbo",
        **kwargs
    ) -> LLM:
        """
        Create LLM instance from direct parameters.
        
        This method is useful for creating one-off LLM instances without
        pre-configured providers.
        
        Args:
            api_base: API base URL (if None, uses OpenAI)
            api_key: API key
            model: Model name
            **kwargs: Additional parameters
        
        Returns:
            LLM: The LLM instance
        """
        # Determine provider type
        if api_base:
            provider_type = LLMProviderType.OPENAI_COMPATIBLE
        else:
            provider_type = LLMProviderType.OPENAI
        
        # Create temporary provider config
        temp_config = LLMProviderConfig(
            provider_type=provider_type,
            name="temp",
            api_base=api_base,
            api_key=api_key,
            default_model=model,
            **kwargs
        )
        
        # Create provider and LLM
        provider = create_provider(temp_config)
        return provider.create_llm(model=model, **kwargs)
    
    def add_provider(self, provider_config: LLMProviderConfig) -> None:
        """Add a new provider configuration."""
        with self._lock:
            # Add to config
            self.config.add_provider(provider_config)
            
            # Create provider instance
            try:
                provider = create_provider(provider_config)
                self._providers[provider_config.name] = provider
                self.logger.info(f"Added provider: {provider_config.name}")
            except Exception:
                self.logger.error(f"Failed to add provider {provider_config.name}", exc_info=True)
                raise
    
    def remove_provider(self, name: str) -> bool:
        """Remove a provider."""
        with self._lock:
            if name in self._providers:
                del self._providers[name]
                
                # Clear related cache entries
                if self.cache:
                    # This is a simple approach - in production you might want more sophisticated cache invalidation
                    self.cache.clear()
                
                # Remove from config
                self.config.remove_provider(name)
                
                self.logger.info(f"Removed provider: {name}")
                return True
        return False
    
    async def list_providers(self) -> List[str]:
        """List all available provider IDs from database."""
        try:
            providers = await LLMService.list_llm_providers()
            return [str(provider.provider_id) for provider in providers]
        except Exception as e:
            self.logger.error(f"Failed to list providers: {e}")
            return []

    async def get_provider_info(self, provider_id: str) -> Optional[LLM]:
        """Get provider information from database."""
        try:
            provider = await LLMService.get_llm_provider_by_id(provider_id)
            if provider:
                # Get provider data and filter out fields that shouldn't be passed to create_llm_from_params
                provider_data = provider.to_dict()

                # Extract the fields needed for create_llm_from_params
                llm_params = {
                    'api_base': provider_data.get('api_base'),
                    'api_key': provider_data.get('api_key'),
                    'model': str(provider.default_model),
                    'api_version': provider_data.get('api_version'),
                    'organization': provider_data.get('organization'),
                    'temperature': provider_data.get('temperature', 0.7),
                    'max_tokens': provider_data.get('max_tokens'),
                    'timeout': provider_data.get('timeout', 60),
                    'max_retries': provider_data.get('max_retries', 3),
                    'context_window': provider_data.get('context_window', 4096),
                    'is_chat_model': provider_data.get('is_chat_model', True),
                    'is_function_calling_model': provider_data.get('is_function_calling_model', False),
                }

                # Filter out None values
                llm_params = {k: v for k, v in llm_params.items() if v is not None}

                return self.create_llm_from_params(**llm_params)
            return None
        except Exception as e:
            self.logger.error(f"Failed to get provider info for {provider_id}: {e}")
            return None
  
    def validate_configuration(self) -> List[str]:
        """Validate current configuration and return issues."""
        return self.config.validate()
    
    def clear_cache(self) -> None:
        """Clear LLM instance cache."""
        if self.cache:
            self.cache.clear()
            self.logger.info("Cleared LLM cache")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        if not self.cache:
            return {"enabled": False}
        
        return {
            "enabled": True,
            "size": self.cache.size(),
            "ttl": self.cache.ttl,
            "expired_cleaned": self.cache.cleanup_expired()
        }
    
    def reload_config(self, new_config: Optional[LLMConfig] = None) -> None:
        """Reload configuration and reinitialize providers."""
        with self._lock:
            self.config = new_config or LLMConfig.from_env()
            
            # Clear cache
            if self.cache:
                self.cache.clear()
            
            # Reinitialize cache with new TTL
            if self.config.cache_enabled:
                self.cache = LLMCache(ttl=self.config.cache_ttl)
            else:
                self.cache = None
            
            # Reinitialize providers
            self._providers.clear()
            self._initialize_providers()
            
            self.logger.info("Reloaded LLM configuration")
