"""
LLM Configuration Management.

This module provides configuration classes and utilities for managing
LLM settings, provider configurations, and validation.
"""

import os
from dataclasses import dataclass, field
from typing import Dict, Optional, Any, List
from enum import Enum


class LLMProviderType(Enum):
    """Supported LLM provider types."""
    OPENAI = "openai"
    OPENAI_COMPATIBLE = "openai_compatible"
    AZURE_OPENAI = "azure_openai"
    ANTHROPIC = "anthropic"
    HUGGINGFACE = "huggingface"


@dataclass
class LLMProviderConfig:
    """Configuration for a specific LLM provider."""
    
    provider_type: LLMProviderType
    name: str
    api_base: Optional[str] = None
    api_key: Optional[str] = None
    api_version: Optional[str] = None
    organization: Optional[str] = None
    default_model: str = "gpt-3.5-turbo"
    context_window: int = 4096
    max_tokens: Optional[int] = None
    temperature: float = 0.7
    timeout: int = 60
    max_retries: int = 3
    is_chat_model: bool = True
    is_function_calling_model: bool = False
    extra_params: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        if self.provider_type == LLMProviderType.OPENAI_COMPATIBLE and not self.api_base:
            raise ValueError("api_base is required for OpenAI-compatible providers")
        
        if not self.api_key:
            # Try to get from environment
            env_key = f"{self.name.upper()}_API_KEY"
            self.api_key = os.getenv(env_key)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "provider_type": self.provider_type.value,
            "name": self.name,
            "api_base": self.api_base,
            "api_key": self.api_key,
            "api_version": self.api_version,
            "organization": self.organization,
            "default_model": self.default_model,
            "context_window": self.context_window,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "is_chat_model": self.is_chat_model,
            "is_function_calling_model": self.is_function_calling_model,
            "extra_params": self.extra_params,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "LLMProviderConfig":
        """Create from dictionary."""
        data = data.copy()
        data["provider_type"] = LLMProviderType(data["provider_type"])
        return cls(**data)


@dataclass
class LLMConfig:
    """Main LLM configuration class."""
    
    providers: Dict[str, LLMProviderConfig] = field(default_factory=dict)
    default_provider: Optional[str] = None
    cache_enabled: bool = True
    cache_ttl: int = 3600  # Cache TTL in seconds
    log_requests: bool = False
    
    def __post_init__(self):
        """Initialize with default providers if none provided."""
        if not self.providers:
            self._load_default_providers()
        
        # Set default provider if not specified
        if not self.default_provider and self.providers:
            self.default_provider = next(iter(self.providers.keys()))
    
    def _load_default_providers(self):
        """Load default provider configurations from environment."""
        # OpenAI provider
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key:
            self.providers["openai"] = LLMProviderConfig(
                provider_type=LLMProviderType.OPENAI,
                name="openai",
                api_key=openai_key,
                default_model="gpt-3.5-turbo",
                is_function_calling_model=True,
            )
        
        # Default OpenAI-compatible provider
        default_api_base = os.getenv("DEFAULT_API_BASE")
        default_api_key = os.getenv("DEFAULT_API_KEY")
        default_model = os.getenv("DEFAULT_MODEL", "gpt-3.5-turbo")
        
        if default_api_base and default_api_key:
            self.providers["default"] = LLMProviderConfig(
                provider_type=LLMProviderType.OPENAI_COMPATIBLE,
                name="default",
                api_base=default_api_base,
                api_key=default_api_key,
                default_model=default_model,
                context_window=128000,
                is_function_calling_model=True,
            )
    
    def add_provider(self, provider_config: LLMProviderConfig) -> None:
        """Add a new provider configuration."""
        self.providers[provider_config.name] = provider_config
        
        # Set as default if it's the first provider
        if not self.default_provider:
            self.default_provider = provider_config.name
    
    def get_provider(self, name: Optional[str] = None) -> Optional[LLMProviderConfig]:
        """Get provider configuration by name or default."""
        if name is None:
            name = self.default_provider
        
        return self.providers.get(name) if name else None
    
    def remove_provider(self, name: str) -> bool:
        """Remove a provider configuration."""
        if name in self.providers:
            del self.providers[name]
            
            # Update default if removed provider was default
            if self.default_provider == name:
                self.default_provider = next(iter(self.providers.keys())) if self.providers else None
            
            return True
        return False
    
    def list_providers(self) -> List[str]:
        """List all available provider names."""
        return list(self.providers.keys())
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []
        
        if not self.providers:
            issues.append("No LLM providers configured")
        
        for name, provider in self.providers.items():
            if not provider.api_key:
                issues.append(f"Provider '{name}' missing API key")
            
            if provider.provider_type == LLMProviderType.OPENAI_COMPATIBLE and not provider.api_base:
                issues.append(f"Provider '{name}' missing API base URL")
        
        if self.default_provider and self.default_provider not in self.providers:
            issues.append(f"Default provider '{self.default_provider}' not found in providers")
        
        return issues
    
    @classmethod
    def from_env(cls) -> "LLMConfig":
        """Create LLM config from environment variables."""
        config = cls()
        config._load_default_providers()
        return config
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "providers": {name: provider.to_dict() for name, provider in self.providers.items()},
            "default_provider": self.default_provider,
            "cache_enabled": self.cache_enabled,
            "cache_ttl": self.cache_ttl,
            "log_requests": self.log_requests,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "LLMConfig":
        """Create from dictionary."""
        data = data.copy()
        providers = {}
        for name, provider_data in data.get("providers", {}).items():
            providers[name] = LLMProviderConfig.from_dict(provider_data)
        
        data["providers"] = providers
        return cls(**data)
