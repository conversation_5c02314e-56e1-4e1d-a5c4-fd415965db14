"""
LLM Module for Easy Agent Center.

This module provides comprehensive LLM (Large Language Model) management functionality,
including configuration management, provider support, instance caching, and agent management.
"""

from .llm_config import LLMConfig, LLMProviderConfig
from .llm_manager import LLMManager
from .providers import LLMProvider, OpenAIProvider, OpenAICompatibleProvider
from .api import router as llm_router
from .models import (
    # LLM Provider models
    CreateLLMProviderRequest,
    UpdateLLMProviderRequest,
    LLMProviderResponse,
    LLMProviderSafeResponse,
    LLMProviderDeleteResponse,
    LLMProviderSetDefaultResponse,
    # Agent models
    CreateAgentRequest,
    UpdateAgentRequest,
    AgentResponse,
    AgentSafeResponse,
    AgentDeleteResponse,
    AgentSetDefaultResponse,
    ChatRequest,
    ChatResponse,
    ChatHistoryResponse,
    ChatMessageResponse
)

__all__ = [
    # Core classes
    "LLMConfig",
    "LLMProviderConfig",
    "LLMManager",
    "LLMProvider",
    "OpenAIProvider",
    "OpenAICompatibleProvider",
    # Routers
    "llm_router",
    # LLM Provider models
    "CreateLLMProviderRequest",
    "UpdateLLMProviderRequest",
    "LLMProviderResponse",
    "LLMProviderSafeResponse",
    "LLMProviderDeleteResponse",
    "LLMProviderSetDefaultResponse",
    # Agent models
    "CreateAgentRequest",
    "UpdateAgentRequest",
    "AgentResponse",
    "AgentSafeResponse",
    "AgentDeleteResponse",
    "AgentSetDefaultResponse",
    "ChatRequest",
    "ChatResponse",
    "ChatHistoryResponse",
    "ChatMessageResponse",
    # Functions
    "get_llm_manager"
]

# Global LLM manager instance
_llm_manager = None

def get_llm_manager() -> LLMManager:
    """Get the global LLM manager instance."""
    global _llm_manager
    if _llm_manager is None:
        _llm_manager = LLMManager()
    return _llm_manager
