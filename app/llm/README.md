# LLM Module Documentation

The LLM module provides comprehensive Large Language Model management functionality for Easy Agent Center, including configuration management, provider support, and instance caching.

## Features

- **Multi-Provider Support**: OpenAI, OpenAI-compatible services, Azure OpenAI
- **Configuration Management**: Environment-based configuration with validation
- **Instance Caching**: Automatic caching of LLM instances with TTL support
- **Provider Abstraction**: Unified interface for different LLM providers
- **Error Handling**: Comprehensive error handling and logging

## Quick Start

### Basic Usage

```python
from llm import get_llm_manager

# Get the global LLM manager
llm_manager = get_llm_manager()

# Create an LLM instance using default provider
llm = llm_manager.create_llm(model="gpt-3.5-turbo")

# Create LLM from direct parameters
llm = llm_manager.create_llm_from_params(
    api_base="http://localhost:11434/v1",
    api_key="ollama",
    model="llama2"
)
```

### Configuration

The LLM module automatically loads configuration from environment variables:

```bash
# OpenAI provider
OPENAI_API_KEY=your_openai_key

# Default OpenAI-compatible provider
DEFAULT_API_BASE=https://api.example.com/v1
DEFAULT_API_KEY=your_api_key
DEFAULT_MODEL=gpt-3.5-turbo
```

## Configuration Classes

### LLMProviderConfig

Represents configuration for a specific LLM provider:

```python
from llm.llm_config import LLMProviderConfig, LLMProviderType

config = LLMProviderConfig(
    provider_type=LLMProviderType.OPENAI_COMPATIBLE,
    name="my_provider",
    api_base="http://localhost:11434/v1",
    api_key="your_key",
    default_model="llama2",
    context_window=4096,
    temperature=0.7,
    is_function_calling_model=True
)
```

### LLMConfig

Main configuration class that manages multiple providers:

```python
from llm.llm_config import LLMConfig

# Create from environment
config = LLMConfig.from_env()

# Add a custom provider
config.add_provider(provider_config)

# Validate configuration
issues = config.validate()
if issues:
    print("Configuration issues:", issues)
```

## LLM Manager

The `LLMManager` class is the main interface for LLM management:

### Creating LLM Instances

```python
from llm import LLMManager

manager = LLMManager()

# Use default provider
llm = manager.create_llm(model="gpt-4")

# Use specific provider
llm = manager.create_llm(provider_name="my_provider", model="custom-model")

# Create from parameters (bypasses configuration)
llm = manager.create_llm_from_params(
    api_base="http://localhost:8000",
    api_key="key",
    model="model"
)
```

### Provider Management

```python
# List available providers
providers = manager.list_providers()

# Get provider information
info = manager.get_provider_info("openai")

# Add new provider
from llm.llm_config import LLMProviderConfig, LLMProviderType

new_provider = LLMProviderConfig(
    provider_type=LLMProviderType.OPENAI_COMPATIBLE,
    name="local_llm",
    api_base="http://localhost:8000",
    api_key="local_key"
)
manager.add_provider(new_provider)

# Remove provider
manager.remove_provider("provider_name")
```

### Cache Management

```python
# Get cache statistics
stats = manager.get_cache_stats()
print(f"Cache size: {stats['size']}")

# Clear cache
manager.clear_cache()

# Disable caching for specific call
llm = manager.create_llm(model="gpt-3.5-turbo", use_cache=False)
```

## Supported Providers

### OpenAI

```python
config = LLMProviderConfig(
    provider_type=LLMProviderType.OPENAI,
    name="openai",
    api_key="your_openai_key",
    default_model="gpt-3.5-turbo",
    organization="your_org_id"  # Optional
)
```

### OpenAI-Compatible Services

```python
config = LLMProviderConfig(
    provider_type=LLMProviderType.OPENAI_COMPATIBLE,
    name="ollama",
    api_base="http://localhost:11434/v1",
    api_key="ollama",
    default_model="llama2",
    context_window=4096,
    is_function_calling_model=False
)
```

### Azure OpenAI

```python
config = LLMProviderConfig(
    provider_type=LLMProviderType.AZURE_OPENAI,
    name="azure",
    api_base="https://your-resource.openai.azure.com",
    api_key="your_azure_key",
    api_version="2023-12-01-preview",
    default_model="gpt-35-turbo"
)
```

## Integration with AgentManager

The LLM module is automatically integrated with the AgentManager:

```python
from agents.agent_manager import AgentManager

manager = AgentManager()

# Create agent with specific LLM provider
agent = manager.create_agent(
    agent_id="my_agent",
    llm_id="openai",  # Use provider name
    model="gpt-4"
)

# Create agent with direct LLM parameters
agent = manager.create_agent(
    agent_id="local_agent",
    api_base="http://localhost:11434/v1",
    api_key="ollama",
    model="llama2"
)

# Access LLM manager from AgentManager
llm_manager = manager.get_llm_manager()
providers = manager.list_llm_providers()
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | OpenAI API key | None |
| `DEFAULT_API_BASE` | Default API base URL | None |
| `DEFAULT_API_KEY` | Default API key | None |
| `DEFAULT_MODEL` | Default model name | gpt-3.5-turbo |

## Error Handling

The LLM module provides comprehensive error handling:

```python
try:
    llm = manager.create_llm(provider_name="nonexistent")
except ValueError as e:
    print(f"Provider error: {e}")

try:
    llm = manager.create_llm_from_params(
        api_base="invalid_url",
        api_key="invalid_key"
    )
except Exception as e:
    print(f"LLM creation failed: {e}")
```

## Logging

The module uses structured logging:

```python
import logging

# Enable debug logging to see cache hits/misses
logging.getLogger("llm").setLevel(logging.DEBUG)
```

## Best Practices

1. **Use Environment Variables**: Configure providers through environment variables for security
2. **Enable Caching**: Keep caching enabled for better performance
3. **Validate Configuration**: Always validate configuration before deployment
4. **Handle Errors**: Implement proper error handling for LLM creation
5. **Monitor Cache**: Monitor cache statistics for optimization

## Examples

See the `examples/` directory for complete usage examples:

- `basic_llm_usage.py` - Basic LLM creation and usage
- `custom_provider.py` - Adding custom providers
- `cache_management.py` - Cache management examples
- `agent_integration.py` - Integration with AgentManager
