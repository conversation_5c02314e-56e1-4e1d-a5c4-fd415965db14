"""
Pydantic models for LLM and Agent API endpoints.

This module contains all the request and response models used by the LLM and Agent API endpoints.
"""

from pydantic import BaseModel
from typing import Optional, Dict, Any, List


class CreateLLMProviderRequest(BaseModel):
    """Request model for creating a new LLM provider."""
    provider_id: str
    name: str
    provider_type: str  # openai, openai_compatible, azure_openai, etc.
    description: Optional[str] = None
    api_base: Optional[str] = None
    api_key: Optional[str] = None
    api_version: Optional[str] = None
    organization: Optional[str] = None
    default_model: str = "gpt-3.5-turbo"
    context_window: int = 4096
    max_tokens: Optional[int] = None
    temperature: float = 0.7
    timeout: int = 60
    max_retries: int = 3
    is_chat_model: bool = True
    is_function_calling_model: bool = False
    set_as_default: bool = False
    extra_params: Optional[Dict[str, Any]] = None


class UpdateLLMProviderRequest(BaseModel):
    """Request model for updating an existing LLM provider."""
    name: Optional[str] = None
    description: Optional[str] = None
    api_base: Optional[str] = None
    api_key: Optional[str] = None
    api_version: Optional[str] = None
    organization: Optional[str] = None
    default_model: Optional[str] = None
    context_window: Optional[int] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    timeout: Optional[int] = None
    max_retries: Optional[int] = None
    is_chat_model: Optional[bool] = None
    is_function_calling_model: Optional[bool] = None
    set_as_default: Optional[bool] = None
    extra_params: Optional[Dict[str, Any]] = None


class LLMProviderResponse(BaseModel):
    """Full response model for LLM provider (includes sensitive information)."""
    id: int
    provider_id: str
    name: str
    description: Optional[str]
    provider_type: str
    api_base: Optional[str]
    api_key: Optional[str]  # This will be masked in the safe version
    api_version: Optional[str]
    organization: Optional[str]
    default_model: str
    context_window: int
    max_tokens: Optional[int]
    temperature: float
    timeout: int
    max_retries: int
    is_chat_model: bool
    is_function_calling_model: bool
    is_active: bool
    is_default: bool
    extra_params: Optional[Dict[str, Any]]
    created_at: str
    updated_at: str


class LLMProviderSafeResponse(BaseModel):
    """Safe response model for LLM provider (without sensitive information)."""
    id: int
    provider_id: str
    name: str
    description: Optional[str]
    provider_type: str
    api_base: Optional[str]
    api_version: Optional[str]
    organization: Optional[str]
    default_model: str
    context_window: int
    max_tokens: Optional[int]
    temperature: float
    timeout: int
    max_retries: int
    is_chat_model: bool
    is_function_calling_model: bool
    is_active: bool
    is_default: bool
    extra_params: Optional[Dict[str, Any]]
    created_at: str
    updated_at: str


class LLMProviderDeleteResponse(BaseModel):
    """Response model for LLM provider deletion."""
    message: str


class LLMProviderSetDefaultResponse(BaseModel):
    """Response model for setting default LLM provider."""
    message: str


# Agent-related models

class CreateAgentRequest(BaseModel):
    """Request model for creating a new agent."""
    agent_id: str
    agent_type: str = "react"
    name: Optional[str] = None
    description: Optional[str] = None
    system_prompt: Optional[str] = None
    set_as_default: bool = False
    llm_id: Optional[str] = None  # LLM provider ID
    api_base: Optional[str] = None
    api_key: Optional[str] = None
    model: str = "gpt-3.5-turbo"


class UpdateAgentRequest(BaseModel):
    """Request model for updating an existing agent."""
    name: Optional[str] = None
    description: Optional[str] = None
    system_prompt: Optional[str] = None
    set_as_default: Optional[bool] = None
    llm_id: Optional[str] = None
    api_base: Optional[str] = None
    api_key: Optional[str] = None
    model: Optional[str] = None


class AgentResponse(BaseModel):
    """Response model for agent information."""
    agent_id: str
    name: str
    description: Optional[str]
    agent_type: str
    system_prompt: Optional[str]
    model: str
    api_base: Optional[str]
    tools: List[str]
    is_default: bool
    is_active: bool
    created_at: str
    updated_at: str


class AgentSafeResponse(BaseModel):
    """Safe response model for agent information (without sensitive data)."""
    agent_id: str
    name: str
    description: Optional[str]
    agent_type: str
    system_prompt: Optional[str]
    model: str
    api_base: Optional[str]
    tools: List[str]
    is_default: bool
    is_active: bool
    created_at: str
    updated_at: str


class AgentDeleteResponse(BaseModel):
    """Response model for agent deletion."""
    message: str


class AgentSetDefaultResponse(BaseModel):
    """Response model for setting default agent."""
    message: str


class ChatRequest(BaseModel):
    """Request model for chat with agent."""
    message: str
    agent_id: Optional[str] = None
    session_id: Optional[str] = None


class ChatResponse(BaseModel):
    """Response model for chat with agent."""
    response: str
    agent_id: str
    agent_name: str


class ChatHistoryResponse(BaseModel):
    """Response model for chat history."""
    id: int
    session_id: str
    agent_id: str
    title: Optional[str]
    created_at: str
    updated_at: str
    message_count: int


class ChatMessageResponse(BaseModel):
    """Response model for chat message."""
    id: int
    role: str
    content: str
    created_at: str
    metadata: Optional[Dict[str, Any]] = None
