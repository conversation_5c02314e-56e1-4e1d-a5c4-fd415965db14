"""
FastAPI routes for LLM provider management.

This module contains all the API endpoints for managing LLM providers,
including CRUD operations and configuration management.
"""

from fastapi import APIRouter, HTTPException
from typing import List

from app.logger.logger import get_logger, PerformanceLogger
from app.database.services import LLMService
from .models import (
    CreateLLMProviderRequest,
    UpdateLLMProviderRequest,
    LLMProviderSafeResponse,
    LLMProviderDeleteResponse,
    LLMProviderSetDefaultResponse
)

# Initialize router and logger
router = APIRouter(prefix="/llms", tags=["LLM Providers"])
api_logger = get_logger("llm.api")


@router.get("", response_model=List[LLMProviderSafeResponse])
async def list_llm_providers():
    """List all LLM providers (without sensitive information)."""
    try:
        providers = await LLMService.list_llm_providers()
        return [LLMProviderSafeResponse(**provider.to_dict_safe()) for provider in providers]
    except Exception as e:
        api_logger.error("Failed to list LLM providers", exception=e)
        raise HTTPException(status_code=500, detail=f"Error listing LLM providers: {str(e)}")


@router.post("", response_model=LLMProviderSafeResponse)
async def create_llm_provider(request: CreateLLMProviderRequest):
    """Create a new LLM provider."""
    try:
        with PerformanceLogger(f"api_create_llm_provider_{request.provider_id}", api_logger):
            provider = await LLMService.create_llm_provider(
                provider_id=request.provider_id,
                name=request.name,
                provider_type=request.provider_type,
                description=request.description,
                api_base=request.api_base,
                api_key=request.api_key,
                api_version=request.api_version,
                organization=request.organization,
                default_model=request.default_model,
                context_window=request.context_window,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                timeout=request.timeout,
                max_retries=request.max_retries,
                is_chat_model=request.is_chat_model,
                is_function_calling_model=request.is_function_calling_model,
                is_default=request.set_as_default,
                extra_params=request.extra_params
            )

            api_logger.info(f"Created LLM provider: {request.provider_id}")
            return LLMProviderSafeResponse(**provider.to_dict_safe())
    except ValueError as e:
        api_logger.warning(f"LLM provider creation failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        api_logger.error(f"LLM provider creation failed for {request.provider_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error creating LLM provider: {str(e)}")


@router.get("/default", response_model=LLMProviderSafeResponse)
async def get_default_llm_provider():
    """Get the default LLM provider."""
    try:
        provider = await LLMService.get_default_llm_provider()
        if not provider:
            raise HTTPException(status_code=404, detail="No default LLM provider found")
        return LLMProviderSafeResponse(**provider.to_dict_safe())
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error("Failed to get default LLM provider", exception=e)
        raise HTTPException(status_code=500, detail=f"Error getting default LLM provider: {str(e)}")


@router.get("/{provider_id}", response_model=LLMProviderSafeResponse)
async def get_llm_provider(provider_id: str):
    """Get a specific LLM provider (without sensitive information)."""
    try:
        provider = await LLMService.get_llm_provider_by_id(provider_id)
        if not provider:
            raise HTTPException(status_code=404, detail=f"LLM provider not found: {provider_id}")
        return LLMProviderSafeResponse(**provider.to_dict_safe())
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get LLM provider: {provider_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error getting LLM provider: {str(e)}")


@router.put("/{provider_id}", response_model=LLMProviderSafeResponse)
async def update_llm_provider(provider_id: str, request: UpdateLLMProviderRequest):
    """Update an LLM provider."""
    try:
        with PerformanceLogger(f"api_update_llm_provider_{provider_id}", api_logger):
            # Filter out None values
            updates = {k: v for k, v in request.model_dump().items() if v is not None}

            # Handle set_as_default field
            if 'set_as_default' in updates:
                updates['is_default'] = updates.pop('set_as_default')

            if not updates:
                raise HTTPException(status_code=400, detail="No valid fields to update")

            provider = await LLMService.update_llm_provider(provider_id, **updates)
            if not provider:
                raise HTTPException(status_code=404, detail=f"LLM provider not found: {provider_id}")

            api_logger.info(f"Updated LLM provider: {provider_id}")
            return LLMProviderSafeResponse(**provider.to_dict_safe())
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to update LLM provider: {provider_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error updating LLM provider: {str(e)}")


@router.delete("/{provider_id}", response_model=LLMProviderDeleteResponse)
async def delete_llm_provider(provider_id: str):
    """Delete an LLM provider."""
    try:
        success = await LLMService.delete_llm_provider(provider_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"LLM provider not found: {provider_id}")

        api_logger.info(f"Deleted LLM provider: {provider_id}")
        return LLMProviderDeleteResponse(message=f"LLM provider '{provider_id}' deleted successfully")
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to delete LLM provider: {provider_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error deleting LLM provider: {str(e)}")


@router.post("/{provider_id}/set-default", response_model=LLMProviderSetDefaultResponse)
async def set_default_llm_provider(provider_id: str):
    """Set an LLM provider as default."""
    try:
        success = await LLMService.set_default_llm_provider(provider_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"LLM provider not found: {provider_id}")

        api_logger.info(f"Set default LLM provider: {provider_id}")
        return LLMProviderSetDefaultResponse(message=f"LLM provider '{provider_id}' set as default successfully")
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to set default LLM provider: {provider_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error setting default LLM provider: {str(e)}")


