"""
MCP (Model Context Protocol) API endpoints.
"""

from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from ..logger.logger import get_logger, PerformanceLogger
from ..database.services import MCPService
from .models import (
    CreateMCPServerRequest,
    UpdateMCPServerRequest,
    MCPServerResponse,
    MCPServerSafeResponse,
    MCPServerDeleteResponse,
    MCPToolResponse,
    MCPServerStatus
)
from .mcp_manager import mcp_manager

# Create router
router = APIRouter(prefix="/mcp", tags=["MCP"])

# Initialize logger
api_logger = get_logger("mcp.api")


@router.post("/servers", response_model=MCPServerSafeResponse)
async def create_mcp_server(request: CreateMCPServerRequest):
    """Create a new MCP server."""
    try:
        with PerformanceLogger(f"api_create_mcp_server_{request.server_id}", api_logger):
            server = await MCPService.create_mcp_server(
                server_id=request.server_id,
                name=request.name,
                description=request.description,
                command=request.command,
                args=request.args,
                env=request.env,
                is_active=request.is_active,
                auto_start=request.auto_start,
                timeout=request.timeout,
                max_retries=request.max_retries,
                extra_config=request.extra_config
            )

            # Add server to MCP manager
            from .models import MCPServerConfig
            config = MCPServerConfig(
                server_id=request.server_id,
                name=request.name,
                description=request.description,
                command=request.command,
                args=request.args,
                env=request.env,
                is_active=request.is_active,
                auto_start=request.auto_start,
                timeout=request.timeout,
                max_retries=request.max_retries,
                extra_config=request.extra_config
            )
            mcp_manager.add_server(config)

            api_logger.info(f"Created MCP server: {request.server_id}")
            return MCPServerSafeResponse(**server.to_dict_safe())
    except ValueError as e:
        api_logger.warning(f"MCP server creation failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        api_logger.error(f"MCP server creation failed for {request.server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error creating MCP server: {str(e)}")


@router.get("/servers", response_model=List[MCPServerSafeResponse])
async def list_mcp_servers():
    """List all MCP servers."""
    try:
        with PerformanceLogger("api_list_mcp_servers", api_logger):
            servers = await MCPService.list_mcp_servers()
            api_logger.info(f"Listed {len(servers)} MCP servers")
            return [MCPServerSafeResponse(**server.to_dict_safe()) for server in servers]
    except Exception as e:
        api_logger.error("Failed to list MCP servers", exception=e)
        raise HTTPException(status_code=500, detail=f"Error listing MCP servers: {str(e)}")


@router.get("/servers/{server_id}", response_model=MCPServerSafeResponse)
async def get_mcp_server(server_id: str):
    """Get a specific MCP server."""
    try:
        with PerformanceLogger(f"api_get_mcp_server_{server_id}", api_logger):
            server = await MCPService.get_mcp_server_by_id(server_id)
            if not server:
                raise HTTPException(status_code=404, detail=f"MCP server '{server_id}' not found")
            
            api_logger.info(f"Retrieved MCP server: {server_id}")
            return MCPServerSafeResponse(**server.to_dict_safe())
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get MCP server {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error getting MCP server: {str(e)}")


@router.put("/servers/{server_id}", response_model=MCPServerSafeResponse)
async def update_mcp_server(server_id: str, request: UpdateMCPServerRequest):
    """Update an MCP server."""
    try:
        with PerformanceLogger(f"api_update_mcp_server_{server_id}", api_logger):
            server = await MCPService.update_mcp_server(
                server_id=server_id,
                name=request.name,
                description=request.description,
                command=request.command,
                args=request.args,
                env=request.env,
                is_active=request.is_active,
                auto_start=request.auto_start,
                timeout=request.timeout,
                max_retries=request.max_retries,
                extra_config=request.extra_config
            )
            
            if not server:
                raise HTTPException(status_code=404, detail=f"MCP server '{server_id}' not found")
            
            api_logger.info(f"Updated MCP server: {server_id}")
            return MCPServerSafeResponse(**server.to_dict_safe())
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to update MCP server {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error updating MCP server: {str(e)}")


@router.delete("/servers/{server_id}", response_model=MCPServerDeleteResponse)
async def delete_mcp_server(server_id: str):
    """Delete an MCP server."""
    try:
        with PerformanceLogger(f"api_delete_mcp_server_{server_id}", api_logger):
            success = await MCPService.delete_mcp_server(server_id)
            if not success:
                raise HTTPException(status_code=404, detail=f"MCP server '{server_id}' not found")
            
            # Remove from MCP manager
            mcp_manager.remove_server(server_id)
            
            api_logger.info(f"Deleted MCP server: {server_id}")
            return MCPServerDeleteResponse(
                message=f"MCP server '{server_id}' deleted successfully",
                server_id=server_id
            )
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to delete MCP server {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error deleting MCP server: {str(e)}")


@router.post("/servers/{server_id}/start")
async def start_mcp_server(server_id: str):
    """Start an MCP server."""
    try:
        with PerformanceLogger(f"api_start_mcp_server_{server_id}", api_logger):
            success = await mcp_manager.start_server(server_id)
            if not success:
                raise HTTPException(status_code=400, detail=f"Failed to start MCP server '{server_id}'")
            
            api_logger.info(f"Started MCP server: {server_id}")
            return {"message": f"MCP server '{server_id}' started successfully", "server_id": server_id}
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to start MCP server {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error starting MCP server: {str(e)}")


@router.post("/servers/{server_id}/stop")
async def stop_mcp_server(server_id: str):
    """Stop an MCP server."""
    try:
        with PerformanceLogger(f"api_stop_mcp_server_{server_id}", api_logger):
            success = await mcp_manager.stop_server(server_id)
            if not success:
                raise HTTPException(status_code=400, detail=f"Failed to stop MCP server '{server_id}'")
            
            api_logger.info(f"Stopped MCP server: {server_id}")
            return {"message": f"MCP server '{server_id}' stopped successfully", "server_id": server_id}
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to stop MCP server {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error stopping MCP server: {str(e)}")


@router.post("/servers/{server_id}/restart")
async def restart_mcp_server(server_id: str):
    """Restart an MCP server."""
    try:
        with PerformanceLogger(f"api_restart_mcp_server_{server_id}", api_logger):
            success = await mcp_manager.restart_server(server_id)
            if not success:
                raise HTTPException(status_code=400, detail=f"Failed to restart MCP server '{server_id}'")
            
            api_logger.info(f"Restarted MCP server: {server_id}")
            return {"message": f"MCP server '{server_id}' restarted successfully", "server_id": server_id}
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to restart MCP server {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error restarting MCP server: {str(e)}")


@router.get("/servers/{server_id}/status", response_model=MCPServerStatus)
async def get_mcp_server_status(server_id: str):
    """Get status of an MCP server."""
    try:
        with PerformanceLogger(f"api_get_mcp_server_status_{server_id}", api_logger):
            status = mcp_manager.get_server_status(server_id)
            if not status:
                raise HTTPException(status_code=404, detail=f"MCP server '{server_id}' not found")
            
            api_logger.info(f"Retrieved MCP server status: {server_id}")
            return status
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get MCP server status {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error getting MCP server status: {str(e)}")


@router.get("/servers/{server_id}/tools", response_model=MCPToolResponse)
async def get_mcp_server_tools(server_id: str):
    """Get tools from an MCP server."""
    try:
        with PerformanceLogger(f"api_get_mcp_server_tools_{server_id}", api_logger):
            tools = mcp_manager.get_server_tools(server_id)
            server_config = mcp_manager.get_server_config(server_id)
            
            if not server_config:
                raise HTTPException(status_code=404, detail=f"MCP server '{server_id}' not found")
            
            # Convert tools to dict format
            tool_dicts = []
            for tool in tools:
                if hasattr(tool, 'metadata') and tool.metadata:
                    tool_dicts.append({
                        "name": tool.metadata.name,
                        "description": tool.metadata.description,
                        "parameters": getattr(tool.metadata, 'fn_schema', {})
                    })
            
            api_logger.info(f"Retrieved {len(tool_dicts)} tools from MCP server: {server_id}")
            return MCPToolResponse(
                server_id=server_id,
                server_name=server_config.name,
                tools=tool_dicts
            )
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get MCP server tools {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"Error getting MCP server tools: {str(e)}")


@router.get("/tools", response_model=List[MCPToolResponse])
async def get_all_mcp_tools():
    """Get all tools from all MCP servers."""
    try:
        with PerformanceLogger("api_get_all_mcp_tools", api_logger):
            all_servers = mcp_manager.list_servers()
            tool_responses = []
            
            for server_id, status in all_servers.items():
                if status.is_connected:
                    tools = mcp_manager.get_server_tools(server_id)
                    server_config = mcp_manager.get_server_config(server_id)
                    
                    if server_config:
                        # Convert tools to dict format
                        tool_dicts = []
                        for tool in tools:
                            if hasattr(tool, 'metadata') and tool.metadata:
                                tool_dicts.append({
                                    "name": tool.metadata.name,
                                    "description": tool.metadata.description,
                                    "parameters": getattr(tool.metadata, 'fn_schema', {})
                                })
                        
                        tool_responses.append(MCPToolResponse(
                            server_id=server_id,
                            server_name=server_config.name,
                            tools=tool_dicts
                        ))
            
            api_logger.info(f"Retrieved tools from {len(tool_responses)} MCP servers")
            return tool_responses
    except Exception as e:
        api_logger.error("Failed to get all MCP tools", exception=e)
        raise HTTPException(status_code=500, detail=f"Error getting MCP tools: {str(e)}")
