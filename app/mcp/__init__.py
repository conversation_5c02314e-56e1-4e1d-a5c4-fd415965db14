"""
MCP (Model Context Protocol) integration module.

This module provides functionality to connect agents with MCP servers,
allowing them to use tools and resources provided by MCP-compatible services.
"""

from .mcp_manager import MCPManager
from .models import (
    MCPServerConfig,
    CreateMCPServerRequest,
    UpdateMCPServerRequest,
    MCPServerResponse,
    MCPServerSafeResponse,
    MCPServerDeleteResponse,
    MCPToolResponse
)

__all__ = [
    "MCPManager",
    "MCPServerConfig",
    "CreateMCPServerRequest",
    "UpdateMCPServerRequest",
    "MCPServerResponse",
    "MCPServerSafeResponse",
    "MCPServerDeleteResponse",
    "MCPToolResponse"
]
