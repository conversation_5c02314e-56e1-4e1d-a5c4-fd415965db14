"""
MCP (Model Context Protocol) data models.
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class MCPTransportType(str, Enum):
    """MCP transport types."""
    STDIO = "stdio"
    SSE = "sse"
    WEBSOCKET = "websocket"


class MCPServerConfig(BaseModel):
    """Configuration for an MCP server."""
    server_id: str = Field(..., description="Unique identifier for the MCP server")
    name: str = Field(..., description="Human-readable name for the server")
    description: Optional[str] = Field(None, description="Description of the server")
    command: str = Field(..., description="Command to start the MCP server")
    args: List[str] = Field(default_factory=list, description="Arguments for the server command")
    env: Dict[str, str] = Field(default_factory=dict, description="Environment variables")
    transport: MCPTransportType = Field(default=MCPTransportType.STDIO, description="Transport type for MCP communication")
    sse_url: Optional[str] = Field(default=None, description="SSE endpoint URL (required for SSE transport)")
    port: Optional[int] = Field(default=None, description="Port number for server (used for SSE/WebSocket)")
    is_active: bool = Field(default=True, description="Whether the server is active")
    auto_start: bool = Field(default=True, description="Whether to auto-start the server")
    timeout: int = Field(default=30, description="Connection timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum connection retries")
    extra_config: Optional[Dict[str, Any]] = Field(None, description="Additional configuration")


class CreateMCPServerRequest(BaseModel):
    """Request model for creating a new MCP server."""
    server_id: str = Field(..., description="Unique identifier for the MCP server")
    name: str = Field(..., description="Human-readable name for the server")
    description: Optional[str] = Field(None, description="Description of the server")
    command: str = Field(..., description="Command to start the MCP server")
    args: List[str] = Field(default_factory=list, description="Arguments for the server command")
    env: Dict[str, str] = Field(default_factory=dict, description="Environment variables")
    transport: MCPTransportType = Field(default=MCPTransportType.STDIO, description="Transport type for MCP communication")
    sse_url: Optional[str] = Field(None, description="SSE endpoint URL (required for SSE transport)")
    port: Optional[int] = Field(None, description="Port number for server (used for SSE/WebSocket)")
    is_active: bool = Field(default=True, description="Whether the server is active")
    auto_start: bool = Field(default=True, description="Whether to auto-start the server")
    timeout: int = Field(default=30, description="Connection timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum connection retries")
    extra_config: Optional[Dict[str, Any]] = Field(None, description="Additional configuration")


class UpdateMCPServerRequest(BaseModel):
    """Request model for updating an MCP server."""
    name: Optional[str] = Field(None, description="Human-readable name for the server")
    description: Optional[str] = Field(None, description="Description of the server")
    command: Optional[str] = Field(None, description="Command to start the MCP server")
    args: Optional[List[str]] = Field(None, description="Arguments for the server command")
    env: Optional[Dict[str, str]] = Field(None, description="Environment variables")
    transport: Optional[MCPTransportType] = Field(None, description="Transport type for MCP communication")
    sse_url: Optional[str] = Field(None, description="SSE endpoint URL (required for SSE transport)")
    port: Optional[int] = Field(None, description="Port number for server (used for SSE/WebSocket)")
    is_active: Optional[bool] = Field(None, description="Whether the server is active")
    auto_start: Optional[bool] = Field(None, description="Whether to auto-start the server")
    timeout: Optional[int] = Field(None, description="Connection timeout in seconds")
    max_retries: Optional[int] = Field(None, description="Maximum connection retries")
    extra_config: Optional[Dict[str, Any]] = Field(None, description="Additional configuration")


class MCPServerResponse(BaseModel):
    """Full response model for MCP server (includes all information)."""
    id: int
    server_id: str
    name: str
    description: Optional[str]
    command: str
    args: List[str]
    env: Dict[str, str]
    transport: str
    sse_url: Optional[str]
    port: Optional[int]
    is_active: bool
    auto_start: bool
    timeout: int
    max_retries: int
    extra_config: Optional[Dict[str, Any]]
    created_at: str
    updated_at: str


class MCPServerSafeResponse(BaseModel):
    """Safe response model for MCP server (excludes sensitive information)."""
    id: int
    server_id: str
    name: str
    description: Optional[str]
    command: str
    args: List[str]
    transport: str
    sse_url: Optional[str]
    port: Optional[int]
    is_active: bool
    auto_start: bool
    timeout: int
    max_retries: int
    created_at: str
    updated_at: str


class MCPServerDeleteResponse(BaseModel):
    """Response model for MCP server deletion."""
    message: str
    server_id: str


class MCPToolResponse(BaseModel):
    """Response model for MCP tools."""
    server_id: str
    server_name: str
    tools: List[Dict[str, Any]]


class MCPServerStatus(BaseModel):
    """Status information for an MCP server."""
    server_id: str
    name: str
    is_running: bool
    is_connected: bool
    last_error: Optional[str] = None
    uptime: Optional[int] = None  # seconds
    tool_count: int = 0
    resource_count: int = 0
