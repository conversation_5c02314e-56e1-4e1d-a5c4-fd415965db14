"""
SSE (Server-Sent Events) MCP Client implementation.

This module provides an SSE-based client for connecting to MCP servers
that support Server-Sent Events transport.
"""

import asyncio
import json
import uuid
from typing import Dict, Any, Optional, List, AsyncGenerator
import aiohttp
from mcp.client.session import ClientSession
from ..logger.logger import get_logger


class SSEMCPClient:
    """
    SSE-based MCP client for connecting to MCP servers via Server-Sent Events.
    
    This client implements the MCP protocol over SSE transport, allowing
    communication with MCP servers that expose SSE endpoints.
    """
    
    def __init__(self, sse_url: str, timeout: int = 30):
        """
        Initialize the SSE MCP client.
        
        Args:
            sse_url: The SSE endpoint URL of the MCP server
            timeout: Connection timeout in seconds
        """
        self.sse_url = sse_url
        self.timeout = timeout
        self.session: Optional[aiohttp.ClientSession] = None
        self.connected = False
        self.pending_requests: Dict[str, asyncio.Future] = {}
        self.logger = get_logger("mcp.SSEMCPClient")
        
    async def connect(self) -> None:
        """Establish connection to the MCP server via SSE."""
        if self.connected:
            return
            
        try:
            self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout))
            
            # Test connection by making a simple request
            async with self.session.get(self.sse_url) as response:
                if response.status == 200:
                    self.connected = True
                    self.logger.info(f"✅ Connected to MCP server via SSE: {self.sse_url}")
                else:
                    raise ConnectionError(f"Failed to connect to SSE endpoint: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"❌ Failed to connect to MCP server: {e}")
            if self.session:
                await self.session.close()
                self.session = None
            raise
    
    async def disconnect(self) -> None:
        """Disconnect from the MCP server."""
        if self.session:
            await self.session.close()
            self.session = None
        self.connected = False
        self.logger.info("🔌 Disconnected from MCP server")
    
    async def _send_request(self, method: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Send a JSON-RPC request to the MCP server via SSE.
        
        Args:
            method: The RPC method name
            params: Optional parameters for the method
            
        Returns:
            The response from the server
        """
        if not self.connected or not self.session:
            await self.connect()
        
        request_id = str(uuid.uuid4())
        request_data = {
            "jsonrpc": "2.0",
            "id": request_id,
            "method": method,
            "params": params or {}
        }
        
        try:
            # For SSE, we typically send requests via POST and receive responses via SSE stream
            async with self.session.post(
                self.sse_url.replace('/sse', '/rpc'),  # Assume RPC endpoint
                json=request_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if "error" in result:
                        raise Exception(f"MCP server error: {result['error']}")
                    return result.get("result", {})
                else:
                    raise Exception(f"HTTP error: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"❌ Request failed: {e}")
            raise
    
    async def list_tools(self) -> Dict[str, Any]:
        """List all available tools from the MCP server."""
        try:
            result = await self._send_request("tools/list")
            self.logger.debug(f"📋 Listed {len(result.get('tools', []))} tools")
            return result
        except Exception as e:
            self.logger.error(f"❌ Failed to list tools: {e}")
            return {"tools": []}
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Call a specific tool on the MCP server.
        
        Args:
            tool_name: Name of the tool to call
            arguments: Arguments to pass to the tool
            
        Returns:
            The tool execution result
        """
        try:
            params = {
                "name": tool_name,
                "arguments": arguments
            }
            result = await self._send_request("tools/call", params)
            self.logger.debug(f"🔧 Called tool '{tool_name}' successfully")
            return result
        except Exception as e:
            self.logger.error(f"❌ Failed to call tool '{tool_name}': {e}")
            raise
    
    async def list_resources(self) -> Dict[str, Any]:
        """List all available resources from the MCP server."""
        try:
            result = await self._send_request("resources/list")
            self.logger.debug(f"📚 Listed {len(result.get('resources', []))} resources")
            return result
        except Exception as e:
            self.logger.error(f"❌ Failed to list resources: {e}")
            return {"resources": []}
    
    async def list_resource_templates(self) -> Dict[str, Any]:
        """List all available resource templates from the MCP server."""
        try:
            result = await self._send_request("resources/templates/list")
            self.logger.debug(f"📋 Listed {len(result.get('resourceTemplates', []))} resource templates")
            return result
        except Exception as e:
            self.logger.error(f"❌ Failed to list resource templates: {e}")
            return {"resourceTemplates": []}
    
    async def read_resource(self, uri: str) -> Dict[str, Any]:
        """
        Read a specific resource from the MCP server.
        
        Args:
            uri: URI of the resource to read
            
        Returns:
            The resource content
        """
        try:
            params = {"uri": uri}
            result = await self._send_request("resources/read", params)
            self.logger.debug(f"📖 Read resource '{uri}' successfully")
            return result
        except Exception as e:
            self.logger.error(f"❌ Failed to read resource '{uri}': {e}")
            raise
    
    async def get_server_info(self) -> Dict[str, Any]:
        """Get information about the MCP server."""
        try:
            result = await self._send_request("initialize", {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {},
                    "resources": {}
                },
                "clientInfo": {
                    "name": "easy-agent-center",
                    "version": "1.0.0"
                }
            })
            self.logger.debug("ℹ️ Retrieved server info successfully")
            return result
        except Exception as e:
            self.logger.error(f"❌ Failed to get server info: {e}")
            return {}
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()


class SSEMCPClientSession(ClientSession):
    """
    Session wrapper for SSE MCP Client to provide compatibility with McpToolSpec.

    This class provides the interface expected by McpToolSpec while using
    the SSE transport underneath. It implements the ClientSession interface
    required by McpToolSpec.
    """

    def __init__(self, sse_url: str, timeout: int = 30):
        """
        Initialize the SSE MCP client session.

        Args:
            sse_url: The SSE endpoint URL of the MCP server
            timeout: Connection timeout in seconds
        """
        self.client = SSEMCPClient(sse_url, timeout)
        self.logger = get_logger("mcp.SSEMCPClientSession")
    
    async def list_tools(self):
        """List all available tools."""
        return await self.client.list_tools()
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]):
        """Call a specific tool."""
        return await self.client.call_tool(tool_name, arguments)
    
    async def list_resources(self):
        """List all available resources."""
        return await self.client.list_resources()
    
    async def list_resource_templates(self):
        """List all available resource templates."""
        return await self.client.list_resource_templates()
    
    async def read_resource(self, uri: str):
        """Read a specific resource."""
        return await self.client.read_resource(uri)
    
    async def connect(self):
        """Connect to the MCP server."""
        await self.client.connect()
    
    async def disconnect(self):
        """Disconnect from the MCP server."""
        await self.client.disconnect()
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.client.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.disconnect()
