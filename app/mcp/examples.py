"""
MCP (Model Context Protocol) server examples and configurations.

This module provides example configurations for common MCP servers
that can be used with agents.
"""

from typing import Dict, List, Optional
from .models import MCPServerConfig, MCPTransportType


# Example MCP server configurations
EXAMPLE_MCP_SERVERS: Dict[str, MCPServerConfig] = {
    "filesystem": MCPServerConfig(
        server_id="filesystem",
        name="File System Server",
        description="Provides file system operations like reading, writing, and listing files",
        command="npx",
        args=["-y", "@modelcontextprotocol/server-filesystem", "/tmp"],
        env={},
        transport=MCPTransportType.STDIO,
        is_active=True,
        auto_start=True,
        timeout=30,
        max_retries=3,
        extra_config={
            "allowed_directories": ["/tmp", "/home/<USER>/documents"],
            "read_only": False
        }
    ),
    
    "sqlite": MCPServerConfig(
        server_id="sqlite",
        name="SQLite Database Server",
        description="Provides SQLite database operations",
        command="npx",
        args=["-y", "@modelcontextprotocol/server-sqlite", "--db-path", "/tmp/example.db"],
        env={},
        is_active=True,
        auto_start=True,
        timeout=30,
        max_retries=3,
        extra_config={
            "database_path": "/tmp/example.db",
            "read_only": False
        }
    ),
    
    "brave_search": MCPServerConfig(
        server_id="brave_search",
        name="Brave Search Server",
        description="Provides web search capabilities using Brave Search API",
        command="npx",
        args=["-y", "@modelcontextprotocol/server-brave-search"],
        env={
            "BRAVE_API_KEY": "your-brave-api-key-here"
        },
        is_active=False,  # Disabled by default as it requires API key
        auto_start=False,
        timeout=30,
        max_retries=3,
        extra_config={
            "requires_api_key": True,
            "api_key_env": "BRAVE_API_KEY"
        }
    ),
    
    "github": MCPServerConfig(
        server_id="github",
        name="GitHub Server",
        description="Provides GitHub repository operations",
        command="npx",
        args=["-y", "@modelcontextprotocol/server-github"],
        env={
            "GITHUB_PERSONAL_ACCESS_TOKEN": "your-github-token-here"
        },
        is_active=False,  # Disabled by default as it requires API key
        auto_start=False,
        timeout=30,
        max_retries=3,
        extra_config={
            "requires_api_key": True,
            "api_key_env": "GITHUB_PERSONAL_ACCESS_TOKEN"
        }
    ),
    
    "postgres": MCPServerConfig(
        server_id="postgres",
        name="PostgreSQL Server",
        description="Provides PostgreSQL database operations",
        command="npx",
        args=["-y", "@modelcontextprotocol/server-postgres"],
        env={
            "POSTGRES_CONNECTION_STRING": "postgresql://user:password@localhost:5432/dbname"
        },
        is_active=False,  # Disabled by default as it requires database
        auto_start=False,
        timeout=30,
        max_retries=3,
        extra_config={
            "requires_database": True,
            "connection_string_env": "POSTGRES_CONNECTION_STRING"
        }
    ),
    
    "memory": MCPServerConfig(
        server_id="memory",
        name="Memory Server",
        description="Provides persistent memory storage for conversations",
        command="npx",
        args=["-y", "@modelcontextprotocol/server-memory"],
        env={},
        is_active=True,
        auto_start=True,
        timeout=30,
        max_retries=3,
        extra_config={
            "storage_path": "/tmp/mcp_memory.json"
        }
    ),
    
    "time": MCPServerConfig(
        server_id="time",
        name="Time Server",
        description="Provides time and date utilities",
        command="npx",
        args=["-y", "@modelcontextprotocol/server-time"],
        env={},
        is_active=True,
        auto_start=True,
        timeout=30,
        max_retries=3,
        extra_config={}
    ),
    
    "everything": MCPServerConfig(
        server_id="everything",
        name="Everything Search Server",
        description="Provides file search using Everything search engine (Windows only)",
        command="npx",
        args=["-y", "@modelcontextprotocol/server-everything"],
        env={},
        transport=MCPTransportType.STDIO,
        is_active=False,  # Disabled by default as it's Windows-specific
        auto_start=False,
        timeout=30,
        max_retries=3,
        extra_config={
            "platform_specific": "windows"
        }
    ),

    # SSE-based MCP servers
    "filesystem_sse": MCPServerConfig(
        server_id="filesystem_sse",
        name="File System Server (SSE)",
        description="Provides file system operations via SSE transport",
        command="python",
        args=["-m", "mcp_server_filesystem", "--transport", "sse", "--port", "3001"],
        env={},
        transport=MCPTransportType.SSE,
        sse_url="http://localhost:3001/sse",
        port=3001,
        is_active=True,
        auto_start=True,
        timeout=30,
        max_retries=3,
        extra_config={
            "allowed_directories": ["/tmp", "/home/<USER>/documents"],
            "read_only": False,
            "transport_type": "sse"
        }
    ),

    "time_sse": MCPServerConfig(
        server_id="time_sse",
        name="Time Server (SSE)",
        description="Provides time and date utilities via SSE transport",
        command="python",
        args=["-m", "mcp_server_time", "--transport", "sse", "--port", "3002"],
        env={},
        transport=MCPTransportType.SSE,
        sse_url="http://localhost:3002/sse",
        port=3002,
        is_active=True,
        auto_start=True,
        timeout=30,
        max_retries=3,
        extra_config={
            "transport_type": "sse"
        }
    )
}


def get_example_server_config(server_id: str) -> MCPServerConfig:
    """
    Get an example MCP server configuration.
    
    Args:
        server_id: The server ID
        
    Returns:
        MCPServerConfig: The server configuration
        
    Raises:
        KeyError: If server_id is not found
    """
    if server_id not in EXAMPLE_MCP_SERVERS:
        raise KeyError(f"Example MCP server '{server_id}' not found. Available: {list(EXAMPLE_MCP_SERVERS.keys())}")
    
    return EXAMPLE_MCP_SERVERS[server_id]


def list_example_servers() -> List[str]:
    """
    List all available example MCP server IDs.
    
    Returns:
        List[str]: List of server IDs
    """
    return list(EXAMPLE_MCP_SERVERS.keys())


def get_safe_example_servers() -> List[MCPServerConfig]:
    """
    Get example MCP servers that are safe to run without additional configuration.
    
    Returns:
        List[MCPServerConfig]: List of safe server configurations
    """
    safe_servers = []
    for config in EXAMPLE_MCP_SERVERS.values():
        # Check if server requires API keys or external dependencies
        extra_config = config.extra_config or {}
        if not extra_config.get("requires_api_key", False) and not extra_config.get("requires_database", False):
            safe_servers.append(config)
    
    return safe_servers


def create_custom_server_config(
    server_id: str,
    name: str,
    command: str,
    args: Optional[List[str]] = None,
    env: Optional[Dict[str, str]] = None,
    description: Optional[str] = None,
    **kwargs
) -> MCPServerConfig:
    """
    Create a custom MCP server configuration.
    
    Args:
        server_id: Unique server identifier
        name: Human-readable server name
        command: Command to start the server
        args: Command arguments
        env: Environment variables
        description: Server description
        **kwargs: Additional configuration options
        
    Returns:
        MCPServerConfig: The server configuration
    """
    return MCPServerConfig(
        server_id=server_id,
        name=name,
        description=description or f"Custom MCP server: {name}",
        command=command,
        args=args or [],
        env=env or {},
        transport=kwargs.get("transport", MCPTransportType.STDIO),
        sse_url=kwargs.get("sse_url"),
        port=kwargs.get("port"),
        is_active=kwargs.get("is_active", True),
        auto_start=kwargs.get("auto_start", True),
        timeout=kwargs.get("timeout", 30),
        max_retries=kwargs.get("max_retries", 3),
        extra_config=kwargs.get("extra_config")
    )


# Installation instructions for MCP servers
MCP_SERVER_INSTALLATION_GUIDE = {
    "nodejs_required": "Most MCP servers require Node.js to be installed. Install from https://nodejs.org/",
    "npm_packages": {
        "filesystem": "npx -y @modelcontextprotocol/server-filesystem",
        "sqlite": "npx -y @modelcontextprotocol/server-sqlite",
        "brave_search": "npx -y @modelcontextprotocol/server-brave-search",
        "github": "npx -y @modelcontextprotocol/server-github",
        "postgres": "npx -y @modelcontextprotocol/server-postgres",
        "memory": "npx -y @modelcontextprotocol/server-memory",
        "time": "npx -y @modelcontextprotocol/server-time",
        "everything": "npx -y @modelcontextprotocol/server-everything"
    },
    "api_keys_required": {
        "brave_search": "Requires BRAVE_API_KEY environment variable",
        "github": "Requires GITHUB_PERSONAL_ACCESS_TOKEN environment variable"
    },
    "external_dependencies": {
        "postgres": "Requires PostgreSQL database connection",
        "sqlite": "Creates SQLite database file",
        "everything": "Requires Everything search engine (Windows only)"
    }
}
