"""Init

Revision ID: 6ba0316e426f
Revises: 
Create Date: 2025-07-10 14:09:53.389003

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6ba0316e426f'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('agents',
    sa.<PERSON>umn('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('agent_id', sa.String(length=255), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('agent_type', sa.String(length=50), nullable=False),
    sa.Column('system_prompt', sa.Text(), nullable=True),
    sa.Column('model', sa.String(length=255), nullable=False),
    sa.Column('api_base', sa.String(length=500), nullable=True),
    sa.Column('api_key', sa.String(length=500), nullable=True),
    sa.Column('is_default', sa.Boolean(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('tools', sa.JSON(), nullable=True),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_agents_agent_id'), 'agents', ['agent_id'], unique=True)
    op.create_table('chat_histories',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('session_id', sa.String(length=255), nullable=False),
    sa.Column('agent_id', sa.String(length=255), nullable=False),
    sa.Column('title', sa.String(length=500), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chat_histories_agent_id'), 'chat_histories', ['agent_id'], unique=False)
    op.create_index(op.f('ix_chat_histories_session_id'), 'chat_histories', ['session_id'], unique=False)
    op.create_table('chat_messages',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('chat_history_id', sa.Integer(), nullable=False),
    sa.Column('role', sa.String(length=20), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('message_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chat_messages_chat_history_id'), 'chat_messages', ['chat_history_id'], unique=False)
    op.create_table('llm_providers',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('provider_id', sa.String(length=255), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('provider_type', sa.String(length=50), nullable=False),
    sa.Column('api_base', sa.String(length=500), nullable=True),
    sa.Column('api_key', sa.String(length=500), nullable=True),
    sa.Column('api_version', sa.String(length=50), nullable=True),
    sa.Column('organization', sa.String(length=255), nullable=True),
    sa.Column('default_model', sa.String(length=255), nullable=False),
    sa.Column('context_window', sa.Integer(), nullable=False),
    sa.Column('max_tokens', sa.Integer(), nullable=True),
    sa.Column('temperature', sa.Float(), nullable=False),
    sa.Column('timeout', sa.Integer(), nullable=False),
    sa.Column('max_retries', sa.Integer(), nullable=False),
    sa.Column('is_chat_model', sa.Boolean(), nullable=False),
    sa.Column('is_function_calling_model', sa.Boolean(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_default', sa.Boolean(), nullable=False),
    sa.Column('extra_params', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_llm_providers_provider_id'), 'llm_providers', ['provider_id'], unique=True)
    op.create_table('mcp_servers',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('server_id', sa.String(length=255), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('command', sa.String(length=500), nullable=False),
    sa.Column('args', sa.JSON(), nullable=True),
    sa.Column('env', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('auto_start', sa.Boolean(), nullable=False),
    sa.Column('timeout', sa.Integer(), nullable=False),
    sa.Column('max_retries', sa.Integer(), nullable=False),
    sa.Column('extra_config', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_mcp_servers_server_id'), 'mcp_servers', ['server_id'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_mcp_servers_server_id'), table_name='mcp_servers')
    op.drop_table('mcp_servers')
    op.drop_index(op.f('ix_llm_providers_provider_id'), table_name='llm_providers')
    op.drop_table('llm_providers')
    op.drop_index(op.f('ix_chat_messages_chat_history_id'), table_name='chat_messages')
    op.drop_table('chat_messages')
    op.drop_index(op.f('ix_chat_histories_session_id'), table_name='chat_histories')
    op.drop_index(op.f('ix_chat_histories_agent_id'), table_name='chat_histories')
    op.drop_table('chat_histories')
    op.drop_index(op.f('ix_agents_agent_id'), table_name='agents')
    op.drop_table('agents')
    # ### end Alembic commands ###
