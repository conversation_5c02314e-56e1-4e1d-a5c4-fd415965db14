# Easy Agent Center Configuration
# Copy this file to .env and update the values as needed

# =============================================================================
# LLM Configuration
# =============================================================================

# OpenAI API Key (required for OpenAI agents)
OPENAI_API_KEY=your-openai-api-key-here

# Default LLM Configuration (optional)
# These will be used if no specific LLM is provided when creating agents
DEFAULT_API_BASE=https://ark.cn-beijing.volces.com/api/v3/
DEFAULT_API_KEY=your-default-api-key-here
DEFAULT_MODEL=deepseek-v3-250324

# =============================================================================
# MySQL Database Configuration (Optional)
# =============================================================================

# Database connection settings
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your-mysql-password
DB_DATABASE=easy_agent_center

# Database connection pool settings
DB_CHARSET=utf8mb4
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Debug settings
DB_ECHO=false

# =============================================================================
# Application Configuration
# =============================================================================

# Server settings
HOST=0.0.0.0
PORT=8000

# Logging configuration
LOG_LEVEL=INFO
LOG_TO_FILE=true
LOG_DIR=logs

# =============================================================================
# Development/Testing Configuration
# =============================================================================

# Environment (development, production, testing)
ENVIRONMENT=development

# Test database (used for running tests)
TEST_DB_DATABASE=test_easy_agent_center
