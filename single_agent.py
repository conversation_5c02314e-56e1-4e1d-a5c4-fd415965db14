from llama_index.core.agent.workflow import ReActAgent
from llama_index.llms.openai_like import OpenAILike
from llama_index.core.workflow import Context
from llama_index.core.agent.workflow import AgentStream, ToolCallResult

from llama_index.tools.mcp import BasicMCPClient,aget_tools_from_mcp_url


llm = OpenAILike(
    model="deepseek-v3-250324",
    api_base="https://ark.cn-beijing.volces.com/api/v3/",
    api_key="af78ce47-20be-4fa6-a47a-d4e64323b9f0",
    context_window=128000,
    is_chat_model=True,
    is_function_calling_model=True,
)

def multiply(a: float, b: float) -> float:
    """Multiply two numbers and returns the product"""
    return a * b

def add(a: float, b: float) -> float:
    """Add two numbers and returns the sum"""
    return a + b

sse_client = BasicMCPClient("https://pdo-flow.yanxiu.com/mcp/eca1b572-a8d6-477b-b41e-d7ee0198ef74")

async def main():
    tools = await aget_tools_from_mcp_url("https://pdo-flow.yanxiu.com/mcp/eca1b572-a8d6-477b-b41e-d7ee0198ef74", client=sse_client)
    
    agent = ReActAgent(
        tools=[multiply, add, *tools],
        llm=llm,
    )
    ctx = Context(agent)
    handler = agent.run("查看今天有哪些直播", ctx=ctx)

    async for ev in handler.stream_events():
        if isinstance(ev, ToolCallResult):
            print(f"\nCall {ev.tool_name} with {ev.tool_kwargs}\nReturned: {ev.tool_output}")
        if isinstance(ev, AgentStream):
            print(f"{ev.delta}", end="", flush=True)

    # response = await handler
    # print(str(response))
    # print(response.tool_calls)



if __name__ == "__main__":
    import asyncio

    asyncio.run(main())